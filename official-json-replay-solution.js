/**
 * 🎯 官方JSON回放解决方案
 * 
 * 使用Playwright官方的RecorderCollection类进行JSON回放
 * 这是官方推荐的方式，完全由官方维护，零维护成本！
 */

const { chromium } = require('playwright-core');

// 导入官方模块 - 这些都是官方公开的API
const { RecorderCollection } = require('playwright-core/lib/server/recorder/recorderCollection');
const { performAction } = require('playwright-core/lib/server/recorder/recorderRunner');
const { mainFrameForAction, buildFullSelector } = require('playwright-core/lib/server/recorder/recorderUtils');

class OfficialJsonReplayExecutor {
  constructor() {
    this.browser = null;
    this.context = null;
    this.pageAliases = new Map();
    this.recorderCollection = null;
  }

  /**
   * 初始化官方回放器
   */
  async initialize(options = {}) {
    console.log('🚀 初始化官方JSON回放器...');
    
    // 启动浏览器
    this.browser = await chromium.launch({
      headless: false,
      ...options.launchOptions
    });
    
    // 创建上下文
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      ...options.contextOptions
    });
    
    // 创建页面并注册到pageAliases
    const page = await this.context.newPage();
    this.pageAliases.set(page, 'page');
    
    // 🎯 关键：创建官方的RecorderCollection
    this.recorderCollection = new RecorderCollection(this.pageAliases);
    this.recorderCollection.setEnabled(true);
    
    console.log('✅ 官方回放器初始化完成');
    return page;
  }

  /**
   * 执行JSON文件回放
   * @param {string} jsonFilePath - JSON文件路径
   */
  async executeJsonFile(jsonFilePath) {
    const fs = require('fs');
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
    const jsonData = JSON.parse(jsonContent);
    
    return await this.executeJsonData(jsonData);
  }

  /**
   * 执行JSON数据回放
   * @param {Array|Object} jsonData - JSON动作数据
   */
  async executeJsonData(jsonData) {
    console.log('🎬 开始官方JSON回放...');
    
    // 解析动作列表
    const actions = Array.isArray(jsonData) ? jsonData : [jsonData];
    
    for (let i = 0; i < actions.length; i++) {
      const actionData = actions[i];
      console.log(`执行步骤 ${i + 1}/${actions.length}:`, actionData.name);
      
      try {
        // 🎯 关键：转换为官方的ActionInContext格式
        const actionInContext = this._convertToActionInContext(actionData);
        
        // 🎯 使用官方的RecorderCollection.performAction
        await this.recorderCollection.performAction(actionInContext);
        
        console.log(`✅ 步骤 ${i + 1} 执行成功`);
        
      } catch (error) {
        console.error(`❌ 步骤 ${i + 1} 执行失败:`, error.message);
        throw error;
      }
    }
    
    console.log('🎉 官方JSON回放完成！');
  }

  /**
   * 转换JSON数据为官方ActionInContext格式
   * @private
   */
  _convertToActionInContext(actionData) {
    const actionInContext = {
      frame: {
        pageAlias: actionData.pageAlias || 'page',
        framePath: actionData.framePath || []
      },
      action: {
        name: actionData.name,
        signals: actionData.signals || []
      },
      startTime: Date.now()
    };

    // 根据动作类型添加特定属性
    switch (actionData.name) {
      case 'navigate':
        actionInContext.action.url = actionData.url;
        break;
        
      case 'click':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.button = actionData.button || 'left';
        actionInContext.action.modifiers = actionData.modifiers || 0;
        actionInContext.action.clickCount = actionData.clickCount || 1;
        if (actionData.position) {
          actionInContext.action.position = actionData.position;
        }
        break;
        
      case 'fill':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.text = actionData.text;
        break;
        
      case 'press':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.key = actionData.key;
        actionInContext.action.modifiers = actionData.modifiers || 0;
        break;
        
      case 'check':
      case 'uncheck':
        actionInContext.action.selector = actionData.selector;
        break;
        
      case 'select':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.options = actionData.options || [];
        break;
        
      case 'setInputFiles':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.files = actionData.files || [];
        break;
        
      // 断言动作
      case 'assertText':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.text = actionData.text;
        actionInContext.action.substring = actionData.substring !== false;
        break;
        
      case 'assertValue':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.value = actionData.value;
        break;
        
      case 'assertChecked':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.checked = actionData.checked !== false;
        break;
        
      case 'assertVisible':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.visible = actionData.visible !== false;
        break;
    }

    return actionInContext;
  }

  /**
   * 获取当前页面
   */
  getCurrentPage() {
    return [...this.pageAliases.keys()][0];
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.context) {
      await this.context.close();
    }
    if (this.browser) {
      await this.browser.close();
    }
    console.log('🧹 官方回放器已清理');
  }
}

// 使用示例
async function demonstrateOfficialReplay() {
  const executor = new OfficialJsonReplayExecutor();
  
  try {
    // 初始化
    const page = await executor.initialize();
    
    // 示例JSON数据（Playwright官方格式）
    const sampleActions = [
      {
        name: 'navigate',
        url: 'https://example.com',
        pageAlias: 'page',
        framePath: []
      },
      {
        name: 'click',
        selector: 'h1',
        button: 'left',
        modifiers: 0,
        clickCount: 1,
        pageAlias: 'page',
        framePath: []
      }
    ];
    
    // 执行回放
    await executor.executeJsonData(sampleActions);
    
    // 等待查看结果
    await page.waitForTimeout(3000);
    
  } catch (error) {
    console.error('回放失败:', error);
  } finally {
    await executor.cleanup();
  }
}

module.exports = { OfficialJsonReplayExecutor };

// 如果直接运行此文件，执行演示
if (require.main === module) {
  demonstrateOfficialReplay().catch(console.error);
}
