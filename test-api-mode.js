/**
 * 测试 API 模式代码生成补丁
 * 验证在隐藏工具栏的情况下是否能正确生成脚本代码
 */

const { chromium } = require('playwright');

// 设置环境变量（模拟 Electron 环境）
process.env.PW_CODEGEN_NO_INSPECTOR = '1';
process.env.PLAYWRIGHT_ELECTRON_BRIDGE = '1';
process.env.PLAYWRIGHT_API_CODE_GENERATION = '1';

// 模拟 Electron 录制器实例
global.electronPlaywrightRecorder = {
  messageHandler: {
    _handlePlaywrightCodeGenerated(data) {
      console.log('\n📡 API模式代码生成测试结果:');
      console.log(`✅ 收到代码生成数据`);
      console.log(`📊 动作数量: ${data.actionCount}`);
      console.log(`📄 源码数量: ${data.sources.length}`);
      console.log(`🔧 API模式: ${data.isApiMode}`);
      
      // 输出 JavaScript 代码
      const jsSource = data.sources.find(s => s.id === 'javascript');
      if (jsSource) {
        console.log('\n🟢 生成的 JavaScript 代码:');
        console.log('─'.repeat(50));
        console.log(jsSource.text);
        console.log('─'.repeat(50));
      }
      
      // 输出 JSONL 代码
      const jsonSource = data.sources.find(s => s.id === 'jsonl');
      if (jsonSource) {
        console.log('\n🟡 生成的 JSONL 代码:');
        console.log('─'.repeat(50));
        console.log(jsonSource.text);
        console.log('─'.repeat(50));
      }
      
      console.log('\n✅ API模式补丁测试成功！');
    }
  }
};

async function testApiModeCodeGeneration() {
  console.log('🧪 开始测试 API 模式代码生成补丁...');
  
  // 检查补丁是否已应用
  try {
    const recorderAppPath = require.resolve('playwright-core/lib/server/recorder/recorderApp.js');
    const content = require('fs').readFileSync(recorderAppPath, 'utf8');
    
    if (content.includes('ELECTRON_API_MODE_PATCH')) {
      console.log('✅ 补丁已正确应用');
    } else {
      console.log('❌ 补丁未应用，请运行: npx patch-package');
      return;
    }
  } catch (error) {
    console.log('⚠️ 无法检查补丁状态:', error.message);
  }
  
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--start-maximized']
  });
  
  try {
    const context = await browser.newContext();
    
    // 🎯 启用 API 模式录制
    await context._enableRecorder({
      language: 'javascript',
      mode: 'recording',
      recorderMode: 'api',  // 关键：使用API模式
    });
    
    const page = await context.newPage();
    
    console.log('\n🎬 开始录制测试操作...');
    console.log('ℹ️ 工具栏应该是隐藏的（PW_CODEGEN_NO_INSPECTOR=1）');
    
    // 执行一些测试操作
    await page.goto('https://example.com');
    await page.waitForTimeout(1000);
    
    // 模拟点击操作（如果页面有可点击元素）
    try {
      await page.click('a', { timeout: 2000 });
      console.log('🖱️ 执行点击操作');
    } catch {
      console.log('ℹ️ 页面无可点击元素，跳过点击测试');
    }
    
    // 等待一段时间让代码生成
    await page.waitForTimeout(2000);
    
    console.log('\n⏳ 等待代码生成...');
    await page.waitForTimeout(1000);
    
    console.log('\n🔄 测试完成，等待最终代码生成...');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testApiModeCodeGeneration().catch(console.error); 