# 🎯 Playwright API模式代码生成补丁指南

## 📋 问题背景

当使用以下配置时，Playwright 默认不会生成脚本代码：
- 设置 `PW_CODEGEN_NO_INSPECTOR=1` (隐藏工具栏)
- 使用 `recorderMode: 'api'` (API模式)

这是因为代码生成逻辑在 `RecorderApp` 中，而这两种模式都不会创建 `RecorderApp` 实例。

## 🛠️ 解决方案

### 核心思路
**Patch `ProgrammaticRecorderApp`** 类，在 `ActionAdded` 事件中：
1. 收集录制的动作 (actions)
2. 使用官方的代码生成API生成脚本
3. 通过 global 对象发送给 Electron

### 技术实现
```javascript
// 在 ProgrammaticRecorderApp.run() 中添加
const apiModeCodeGenerator = {
  actions: [],
  
  generateAllSources() {
    // 使用官方API生成代码
    const { collapseActions } = require('./recorderUtils');
    const { generateCode } = require('../codegen/language');
    const { languageSet } = require('../codegen/languages');
    
    const collapsedActions = collapseActions(this.actions);
    const sources = [];
    
    for (const languageGenerator of languageSet()) {
      const { header, footer, actionTexts, text } = generateCode(
        collapsedActions, 
        languageGenerator, 
        this.languageGeneratorOptions
      );
      sources.push({
        id: languageGenerator.id,
        text,
        language: languageGenerator.highlighter,
        // ... 其他属性
      });
    }
    return sources;
  }
};
```

## 📦 安装使用

### 1. 应用补丁
```bash
# 确保已安装 patch-package
npm install patch-package --save-dev

# 应用补丁 (patch文件已包含在项目中)
npx patch-package
```

### 2. 设置环境变量
```javascript
// 在你的 Electron 主进程中
process.env.PW_CODEGEN_NO_INSPECTOR = '1';           // 隐藏工具栏
process.env.PLAYWRIGHT_ELECTRON_BRIDGE = '1';       // 启用Electron桥接  
process.env.PLAYWRIGHT_API_CODE_GENERATION = '1';   // 启用API模式代码生成
```

### 3. 启用API模式录制
```javascript
await context._enableRecorder({
  language: 'javascript',
  mode: 'recording',
  recorderMode: 'api',  // 🎯 关键：使用API模式
  outputFile: './output.js'
});
```

### 4. 接收生成的代码
```javascript
// 在 Electron 主进程中设置全局录制器
global.electronPlaywrightRecorder = {
  messageHandler: {
    _handlePlaywrightCodeGenerated(data) {
      console.log('📡 收到API模式生成的代码:', {
        actionCount: data.actionCount,
        sourcesCount: data.sources.length,
        isApiMode: data.isApiMode
      });
      
      // 获取 JavaScript 代码
      const jsSource = data.sources.find(s => s.id === 'javascript');
      if (jsSource) {
        console.log('生成的JS代码:\n', jsSource.text);
      }
      
      // 获取 JSONL 代码 
      const jsonSource = data.sources.find(s => s.id === 'jsonl');
      if (jsonSource) {
        console.log('生成的JSON代码:\n', jsonSource.text);
      }
      
      // 发送到渲染进程或保存到文件
      this.sendToRenderer('playwright-code-generated', data);
    }
  }
};
```

## 🔍 工作流程

1. **用户操作** → 录制浏览器中的动作
2. **Playwright录制** → 生成 `ActionInContext` 对象
3. **ProgrammaticRecorderApp** → 监听 `ActionAdded` 事件
4. **代码生成器** → 使用官方API生成多语言脚本
5. **Global桥接** → 发送到 `global.electronPlaywrightRecorder`
6. **Electron接收** → 处理生成的代码数据

## 📊 生成的数据格式

```javascript
{
  type: 'playwrightCodeGenerated',
  sources: [
    {
      id: 'javascript',
      text: '// 生成的JS代码',
      language: 'javascript',
      label: 'JavaScript',
      // ...
    },
    {
      id: 'jsonl', 
      text: '{"action":"click","selector":"..."}',
      language: 'javascript',
      label: 'JSON',
      // ...
    }
    // ... 其他语言
  ],
  primaryPageURL: 'https://example.com',
  timestamp: 1699123456789,
  mode: 'recording',
  isApiMode: true,
  actionCount: 5
}
```

## ✅ 验证方式

### 检查补丁是否生效
```javascript
// 检查 ProgrammaticRecorderApp 是否包含我们的代码
const recorderAppPath = require.resolve('playwright-core/lib/server/recorder/recorderApp.js');
const content = require('fs').readFileSync(recorderAppPath, 'utf8');
console.log('补丁已应用:', content.includes('ELECTRON_API_MODE_PATCH'));
```

### 测试代码生成
```javascript
// 启动录制并操作页面
await page.click('button');
await page.fill('input', 'test');

// 应该在控制台看到：
// 📡 API模式: 已发送代码生成数据到Electron (动作数量: 2)
```

## 🐛 故障排除

### 1. 代码没有生成
- ✅ 检查环境变量是否正确设置
- ✅ 确认 `global.electronPlaywrightRecorder` 已设置
- ✅ 验证补丁是否正确应用

### 2. 补丁应用失败
```bash
# 清理并重新应用
rm -rf node_modules/playwright-core
npm install
npx patch-package
```

### 3. 代码生成错误
- 检查控制台是否有 "🚨 API模式代码生成失败" 错误
- 确认 Playwright 版本兼容性 (测试版本: 1.54.1)

## 🎖️ 优势

✅ **完全兼容** - 使用官方的代码生成API  
✅ **多语言支持** - 生成 JS、Python、Java、C# 等  
✅ **延迟优化** - 避免频繁代码生成，性能优秀  
✅ **错误处理** - 完善的错误捕获和日志  
✅ **向后兼容** - 不影响原有的RecorderApp功能  

## 📈 效果对比

| 模式 | 工具栏显示 | 代码生成 | JSON格式 | Electron集成 |
|------|------------|----------|----------|-------------|
| 原生codegen | ✅ | ✅ | ✅ | ❌ |
| PW_CODEGEN_NO_INSPECTOR | ❌ | ❌ | ❌ | ❌ |
| recorderMode: 'api' | ❌ | ❌ | ❌ | ❌ |
| **API模式补丁** | ❌ | ✅ | ✅ | ✅ |

这个解决方案完美实现了你的需求：**隐藏工具栏 + 获取官方JSON脚本 + Electron集成**！ 