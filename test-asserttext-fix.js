/**
 * 测试 assertText 修复
 * 验证全局 API 和备用方案都能正确处理断言动作
 */

const { ReliableJsonReplayExecutor } = require('./src/main/reliable-json-replay-executor');

console.log('🧪 测试 assertText 修复');
console.log('='.repeat(50));

async function testAssertTextFix() {
  const executor = new ReliableJsonReplayExecutor();
  
  try {
    console.log('🚀 启动浏览器...');
    await executor.initialize({ 
      browserName: 'chromium', 
      launchOptions: { headless: true } 
    });
    
    // 创建测试页面（使用英文避免编码问题）
    const testHtml = `
      <html>
        <head><meta charset="UTF-8"></head>
        <body>
          <h1 id="title">Test Page Title</h1>
          <div id="content">This is test content</div>
          <p id="partial">Contains partial text in paragraph</p>
          <input id="input" value="input value">
          <input type="checkbox" id="checkbox" checked>
        </body>
      </html>
    `;
    
    const testActions = [
      {
        name: 'navigate',
        url: `data:text/html,${encodeURIComponent(testHtml)}`
      },
      {
        name: 'assertText',
        selector: '#title',
        text: 'Test Page Title',
        substring: false
      },
      {
        name: 'assertText',
        selector: '#content',
        text: 'test content',
        substring: true
      },
      {
        name: 'assertText',
        selector: '#partial',
        text: 'partial text',
        substring: true
      },
      {
        name: 'assertValue',
        selector: '#input',
        value: 'input value'
      },
      {
        name: 'assertChecked',
        selector: '#checkbox',
        checked: true
      },
      {
        name: 'assertVisible',
        selector: '#title'
      }
    ];
    
    console.log(`📋 执行 ${testActions.length} 个测试动作...`);
    
    for (const [index, action] of testActions.entries()) {
      console.log(`\n🎬 执行动作 ${index + 1}: ${action.name} -> ${action.selector}`);
      
      try {
        await executor.executeAction(action);
        console.log(`✅ 动作 ${index + 1} 执行成功`);
      } catch (error) {
        console.log(`❌ 动作 ${index + 1} 执行失败: ${error.message}`);
        throw error;
      }
    }
    
    console.log('\n🎉 所有断言测试通过！');
    
    // 测试失败的断言
    console.log('\n📋 测试失败的断言...');
    
    const failingActions = [
      {
        name: 'assertText',
        selector: '#title',
        text: 'Wrong Title',
        substring: false
      },
      {
        name: 'assertValue',
        selector: '#input',
        value: 'wrong value'
      },
      {
        name: 'assertChecked',
        selector: '#checkbox',
        checked: false
      }
    ];
    
    for (const [index, action] of failingActions.entries()) {
      console.log(`\n🎬 测试失败断言 ${index + 1}: ${action.name}`);
      
      try {
        await executor.executeAction(action);
        console.log(`❌ 断言应该失败但成功了: ${action.name}`);
      } catch (error) {
        console.log(`✅ 断言正确失败: ${error.message.substring(0, 80)}...`);
      }
    }
    
    console.log('\n🎉 断言失败测试也通过！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  } finally {
    await executor.close();
  }
}

async function testGlobalAPIDirectly() {
  console.log('\n📋 直接测试全局 API 断言功能');
  
  const api = global.playwrightReplayAPI;
  
  if (!api) {
    console.log('❌ 全局 API 不可用');
    return;
  }
  
  // 测试 createActionInContext 对断言动作的支持
  const assertActions = [
    {
      name: 'assertText',
      selector: '#test',
      text: 'test text',
      substring: true
    },
    {
      name: 'assertValue',
      selector: '#input',
      value: 'test value'
    },
    {
      name: 'assertChecked',
      selector: '#checkbox',
      checked: true
    },
    {
      name: 'assertVisible',
      selector: '#element'
    }
  ];
  
  for (const actionData of assertActions) {
    try {
      const actionInContext = api.createActionInContext('page', [], actionData);
      console.log(`✅ ${actionData.name} - ActionInContext 创建成功`);
      
      // 验证结构
      if (actionInContext.action.name === actionData.name && 
          actionInContext.action.selector === actionData.selector) {
        console.log(`  ✅ 结构验证通过`);
      } else {
        console.log(`  ⚠️ 结构验证失败`);
      }
      
    } catch (error) {
      console.log(`❌ ${actionData.name} - 创建失败: ${error.message}`);
    }
  }
  
  console.log('✅ 全局 API 断言功能测试完成');
}

async function runAllTests() {
  try {
    console.log('🎯 开始断言修复测试...');
    
    // 测试全局 API
    await testGlobalAPIDirectly();
    
    // 测试实际执行
    await testAssertTextFix();
    
    console.log('\n🏆 所有测试完成！');
    console.log('📋 修复总结:');
    console.log('  ✅ assertText 动作已添加到备用方案');
    console.log('  ✅ assertValue 动作已添加到备用方案');
    console.log('  ✅ assertChecked 动作已添加到备用方案');
    console.log('  ✅ assertVisible 动作已添加到备用方案');
    console.log('  ✅ 全局 API 包装器改进了错误检测');
    console.log('  ✅ 断言失败时提供详细错误信息');
    
    console.log('\n💡 现在支持的断言动作:');
    console.log('  - assertText: 文本断言（支持完全匹配和子字符串匹配）');
    console.log('  - assertValue: 输入值断言');
    console.log('  - assertChecked: 复选框状态断言');
    console.log('  - assertVisible: 元素可见性断言');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testAssertTextFix,
  testGlobalAPIDirectly,
  runAllTests
};
