/**
 * 🛡️ 反反爬虫配置文件
 * 
 * 提供各种反反爬虫策略和配置选项
 */

// 预设的浏览器指纹配置
const FINGERPRINT_PRESETS = {
  // Windows Chrome 用户
  windows_chrome: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport: { width: 1920, height: 1080 },
    timezone: 'America/New_York',
    locale: 'en-US',
    platform: 'Win32',
    webglVendor: 'Google Inc. (NVIDIA)',
    webglRenderer: 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
    hardwareConcurrency: 8,
    deviceMemory: 8
  },

  // macOS Safari 用户
  macos_safari: {
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    viewport: { width: 1440, height: 900 },
    timezone: 'America/Los_Angeles',
    locale: 'en-US',
    platform: 'MacIntel',
    webglVendor: 'Apple Inc.',
    webglRenderer: 'Apple GPU',
    hardwareConcurrency: 4,
    deviceMemory: 8
  },

  // Linux Firefox 用户
  linux_firefox: {
    userAgent: 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0',
    viewport: { width: 1366, height: 768 },
    timezone: 'Europe/London',
    locale: 'en-GB',
    platform: 'Linux x86_64',
    webglVendor: 'Mesa',
    webglRenderer: 'Mesa DRI Intel(R) UHD Graphics 630',
    hardwareConcurrency: 4,
    deviceMemory: 4
  },

  // 移动端 Chrome
  mobile_chrome: {
    userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
    viewport: { width: 360, height: 640 },
    timezone: 'Asia/Shanghai',
    locale: 'zh-CN',
    platform: 'Linux armv8l',
    webglVendor: 'Qualcomm',
    webglRenderer: 'Adreno (TM) 640',
    hardwareConcurrency: 8,
    deviceMemory: 6
  }
};

// 反检测策略配置
const ANTI_DETECTION_STRATEGIES = {
  // 基础策略
  basic: {
    removeWebdriver: true,
    fakeChrome: true,
    fakePlugins: true,
    randomizeCanvas: false,
    fakeWebGL: false,
    humanBehavior: false
  },

  // 中等策略
  moderate: {
    removeWebdriver: true,
    fakeChrome: true,
    fakePlugins: true,
    randomizeCanvas: true,
    fakeWebGL: true,
    humanBehavior: true,
    fakePermissions: true,
    fakeBattery: true
  },

  // 高级策略
  aggressive: {
    removeWebdriver: true,
    fakeChrome: true,
    fakePlugins: true,
    randomizeCanvas: true,
    fakeWebGL: true,
    humanBehavior: true,
    fakePermissions: true,
    fakeBattery: true,
    fakeConnection: true,
    fakeScreen: true,
    fakeFonts: true,
    fakeLanguages: true,
    randomizeTimings: true
  }
};

// 常见反爬虫服务的特征
const ANTI_BOT_SIGNATURES = {
  cloudflare: [
    'cloudflare',
    'cf-ray',
    'cf-cache-status',
    '__cf_bm',
    'cf_clearance',
    'Just a moment',
    'Checking your browser'
  ],
  
  distil: [
    'distil',
    'distil_identifier',
    'distil_r_blocked',
    'distil_r_captcha'
  ],
  
  imperva: [
    'imperva',
    'incapsula',
    '_incap_ses',
    'incap_ses',
    'visid_incap'
  ],
  
  recaptcha: [
    'recaptcha',
    'g-recaptcha',
    'grecaptcha',
    'recaptcha-checkbox'
  ],
  
  datadome: [
    'datadome',
    'dd_cookie_test',
    'dd_session_id'
  ],
  
  perimeterx: [
    'perimeterx',
    '_px',
    '_pxhd',
    'px-captcha'
  ]
};

// 人类行为模拟配置
const HUMAN_BEHAVIOR_CONFIG = {
  // 鼠标移动
  mouseMovement: {
    enabled: true,
    steps: 5,
    minDelay: 20,
    maxDelay: 50,
    randomness: 0.1
  },

  // 键盘输入
  typing: {
    enabled: true,
    minDelay: 50,
    maxDelay: 150,
    mistakes: 0.02, // 2% 打字错误率
    corrections: true
  },

  // 页面滚动
  scrolling: {
    enabled: true,
    speed: 'slow', // slow, medium, fast
    randomPauses: true,
    pauseProbability: 0.3
  },

  // 等待时间
  delays: {
    beforeClick: [100, 300],
    afterClick: [200, 500],
    beforeType: [150, 400],
    afterType: [100, 250],
    pageLoad: [1000, 3000]
  }
};

// 代理配置模板
const PROXY_CONFIGS = {
  // 住宅代理
  residential: {
    type: 'residential',
    rotation: 'session', // session, request
    countries: ['US', 'GB', 'CA', 'AU'],
    sticky: true
  },

  // 数据中心代理
  datacenter: {
    type: 'datacenter',
    rotation: 'request',
    countries: ['US', 'DE', 'NL'],
    sticky: false
  },

  // 移动代理
  mobile: {
    type: 'mobile',
    rotation: 'session',
    countries: ['US', 'GB'],
    sticky: true,
    carriers: ['verizon', 'att', 'tmobile']
  }
};

// 浏览器启动参数（反检测）
const ANTI_DETECTION_ARGS = [
  // 基础反检测
  '--no-first-run',
  '--no-default-browser-check',
  '--disable-blink-features=AutomationControlled',
  '--disable-features=VizDisplayCompositor',
  
  // 性能优化
  '--disable-ipc-flooding-protection',
  '--disable-renderer-backgrounding',
  '--disable-backgrounding-occluded-windows',
  '--disable-client-side-phishing-detection',
  
  // 隐私保护
  '--disable-sync',
  '--metrics-recording-only',
  '--no-report-upload',
  '--disable-dev-shm-usage',
  
  // 扩展管理
  '--disable-extensions-except',
  '--disable-extensions',
  '--disable-component-extensions-with-background-pages',
  '--disable-default-apps',
  
  // 其他
  '--mute-audio',
  '--no-zygote',
  '--disable-background-timer-throttling',
  '--disable-restore-session-state',
  '--disable-translate'
];

// 导出配置
module.exports = {
  FINGERPRINT_PRESETS,
  ANTI_DETECTION_STRATEGIES,
  ANTI_BOT_SIGNATURES,
  HUMAN_BEHAVIOR_CONFIG,
  PROXY_CONFIGS,
  ANTI_DETECTION_ARGS,

  // 工具函数
  getRandomFingerprint() {
    const presets = Object.values(FINGERPRINT_PRESETS);
    return presets[Math.floor(Math.random() * presets.length)];
  },

  getStrategy(level = 'moderate') {
    return ANTI_DETECTION_STRATEGIES[level] || ANTI_DETECTION_STRATEGIES.moderate;
  },

  detectAntiBot(content) {
    const detected = [];
    
    for (const [service, signatures] of Object.entries(ANTI_BOT_SIGNATURES)) {
      for (const signature of signatures) {
        if (content.toLowerCase().includes(signature.toLowerCase())) {
          detected.push(service);
          break;
        }
      }
    }
    
    return [...new Set(detected)]; // 去重
  },

  generateRandomDelay(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  // 生成随机的Canvas噪声
  generateCanvasNoise() {
    return Math.random() * 0.1;
  },

  // 生成随机的WebGL参数
  generateWebGLParams() {
    const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD', 'Apple Inc.'];
    const renderers = [
      'Intel(R) UHD Graphics 630',
      'NVIDIA GeForce GTX 1060',
      'AMD Radeon RX 580',
      'Apple GPU'
    ];
    
    return {
      vendor: vendors[Math.floor(Math.random() * vendors.length)],
      renderer: renderers[Math.floor(Math.random() * renderers.length)]
    };
  }
};
