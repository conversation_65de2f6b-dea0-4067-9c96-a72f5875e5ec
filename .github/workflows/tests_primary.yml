name: "tests 1"

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    paths-ignore:
      - 'browser_patches/**'
      - 'docs/**'
    branches:
      - main
      - release-*

concurrency:
  # For pull requests, cancel all currently-running jobs for this workflow
  # https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#concurrency
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  # Force terminal colors. @see https://www.npmjs.com/package/colors
  FORCE_COLOR: 1
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1
  DEBUG_GIT_COMMIT_INFO: 1

jobs:
  test_linux:
    name: ${{ matrix.os }} (${{ matrix.browser }} - Node.js ${{ matrix.node-version }})
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        os: [ubuntu-22.04]
        node-version: [18]
        include:
          - os: ubuntu-22.04
            node-version: 20
            browser: chromium
          - os: ubuntu-22.04
            node-version: 22
            browser: chromium
          - os: ubuntu-22.04
            node-version: 24
            browser: chromium
    runs-on: ${{ matrix.os }}
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        node-version: ${{ matrix.node-version }}
        browsers-to-install: ${{ matrix.browser }} chromium
        command: npm run test -- --project=${{ matrix.browser }}-*
        bot-name: "${{ matrix.browser }}-${{ matrix.os }}-node${{ matrix.node-version }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}

  test_linux_chromium_tot:
    name: ${{ matrix.os }} (chromium tip-of-tree)
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04]
    runs-on: ${{ matrix.os }}
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: chromium-tip-of-tree
        command: npm run test -- --project=chromium-*
        bot-name: "${{ matrix.os }}-chromium-tip-of-tree"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: chromium-tip-of-tree

  test_test_runner:
    name: Test Runner
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18]
        shardIndex: [1, 2]
        shardTotal: [2]
        include:
          - os: ubuntu-latest
            node-version: 20
            shardIndex: 1
            shardTotal: 2
          - os: ubuntu-latest
            node-version: 20
            shardIndex: 2
            shardTotal: 2
          - os: ubuntu-latest
            node-version: 22
            shardIndex: 1
            shardTotal: 2
          - os: ubuntu-latest
            node-version: 22
            shardIndex: 2
            shardTotal: 2
          - os: ubuntu-latest
            node-version: 24
            shardIndex: 1
            shardTotal: 2
          - os: ubuntu-latest
            node-version: 24
            shardIndex: 2
            shardTotal: 2
    runs-on: ${{ matrix.os }}
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        node-version: ${{matrix.node-version}}
        command: npm run ttest -- --shard ${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
        bot-name: "${{ matrix.os }}-node${{ matrix.node-version }}-${{ matrix.shardIndex }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: firefox-beta

  test_web_components:
    name: Web Components
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - run: npm ci
    - run: npm run build

    - run: npx playwright install --with-deps
    - run: npm run test-html-reporter
      env:
        PWTEST_BOT_NAME: "web-components-html-reporter"
    - name: Upload blob report
      if: ${{ !cancelled() }}
      uses: ./.github/actions/upload-blob-report
      with:
        report_dir: packages/html-reporter/blob-report
        job_name: "web-components-html-reporter"

    - run: npm run test-web
      if: ${{ !cancelled() }}
      env:
        PWTEST_BOT_NAME: "web-components-web"
    - name: Upload blob report
      if: ${{ !cancelled() }}
      uses: ./.github/actions/upload-blob-report
      with:
        report_dir: packages/web/blob-report
        job_name: "web-components-web"

  test_vscode_extension:
    name: VSCode Extension
    runs-on: ubuntu-latest
    env:
      PWTEST_BOT_NAME: "vscode-extension"
      DEBUG_GIT_COMMIT_INFO: ""
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - run: npm ci
      env:
        DEBUG: pw:install
    - run: npm run build
    - run: npx playwright install chromium
    - name: Checkout extension
      run: git clone https://github.com/microsoft/playwright-vscode.git
    - name: Print extension revision
      run: git rev-parse HEAD
      working-directory: ./playwright-vscode
    - name: Remove @playwright/test from extension dependencies
      run: node -e "const p = require('./package.json'); delete p.devDependencies['@playwright/test']; fs.writeFileSync('./package.json', JSON.stringify(p, null, 2));"
      working-directory: ./playwright-vscode
    - name: Build extension
      run: npm ci && npm run build
      working-directory: ./playwright-vscode
    - name: Run extension tests
      run: npm run test -- --workers=1
      working-directory: ./playwright-vscode
    - name: Upload blob report
      if: ${{ !cancelled() }}
      uses: ./.github/actions/upload-blob-report
      with:
        report_dir: playwright-vscode/blob-report
        job_name: ${{ env.PWTEST_BOT_NAME }}

  test_package_installations:
    name: "Installation Test ${{ matrix.os }}"
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        os:
        - ubuntu-latest
        - macos-latest
        - windows-latest
    runs-on: ${{ matrix.os  }}
    timeout-minutes: 30
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    steps:
    - uses: actions/checkout@v4
    - run: npm install -g yarn@1
    - run: npm install -g pnpm@8
    - name: Setup Ubuntu Binary Installation # TODO: Remove when https://github.com/electron/electron/issues/42510 is fixed
      if: ${{ runner.os == 'Linux' }}
      run: |
        if grep -q "Ubuntu 24" /etc/os-release; then
          sudo sysctl -w kernel.apparmor_restrict_unprivileged_userns=0
        fi
      shell: bash
    - uses: ./.github/actions/run-test
      with:
        command: npm run itest
        bot-name: "package-installations-${{ matrix.os }}"
        shell: ${{ matrix.os == 'windows-latest' && 'pwsh' || 'bash' }}
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
