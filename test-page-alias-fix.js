/**
 * 测试页面别名修复
 */

const { FixedOfficialExecutor } = require('./src/main/fixed-official-executor');

async function testPageAliasFix() {
  const executor = new FixedOfficialExecutor({
    enableDiagnostics: true
  });

  try {
    console.log('🧪 测试页面别名修复...\n');

    // 初始化
    await executor.initialize({
      browserName: 'chromium',
      launchOptions: { headless: false },
      contextOptions: {}
    });

    console.log('📊 初始页面状态:');
    console.log('   页面别名:', Array.from(executor.pages.keys()));
    console.log('   页面映射:', Array.from(executor.pageAliases.values()));

    // 测试动作序列，模拟你遇到的问题
    const testActions = [
      {
        name: 'navigate',
        url: 'https://example.com',
        pageAlias: 'page'
      },
      {
        name: 'openPage',
        url: 'https://httpbin.org/html',
        pageAlias: 'page1'  // 这里使用 page1
      },
      {
        name: 'assertVisible',
        selector: 'h1',
        pageAlias: 'page1'  // 在 page1 上执行断言
      },
      {
        name: 'click',
        selector: 'h1',
        pageAlias: 'page'   // 回到原页面
      }
    ];

    console.log('\n🎬 开始执行测试动作...');
    
    for (let i = 0; i < testActions.length; i++) {
      const action = testActions[i];
      console.log(`\n--- 步骤 ${i + 1}: ${action.name} (页面: ${action.pageAlias || 'page'}) ---`);
      
      await executor.executeAction(action);
      
      console.log('📊 当前页面状态:');
      console.log('   页面别名:', Array.from(executor.pages.keys()));
      console.log('   页面映射:', Array.from(executor.pageAliases.values()));
    }

    console.log('\n✅ 所有测试动作执行成功！');
    
    // 等待一下让用户看到结果
    await new Promise(resolve => setTimeout(resolve, 3000));

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await executor.close();
  }
}

// 运行测试
if (require.main === module) {
  testPageAliasFix().catch(console.error);
}

module.exports = { testPageAliasFix };
