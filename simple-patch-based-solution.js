/**
 * 🎯 最简单的解决方案：基于你的patch的全局API + 反反爬虫指纹伪装
 *
 * 直接使用你patch中暴露的global.playwrightReplayAPI
 * 优势：
 * 1. 零额外代码，直接使用patch
 * 2. 完全官方API，维护成本最低
 * 3. 已经过测试验证
 * 4. 内置完整的反反爬虫指纹伪装
 */

const { chromium } = require('playwright-core');
const crypto = require('crypto');

class SimplePatchBasedReplay {
  constructor() {
    this.browser = null;
    this.context = null;
    this.pageAliases = new Map();
    this.fingerprintConfig = null;
  }

  /**
   * 生成随机浏览器指纹配置
   */
  generateRandomFingerprint() {
    const userAgents = [
      // Chrome Windows
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
      // Chrome macOS
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      // Firefox
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
      // Edge
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
    ];

    const viewports = [
      { width: 1920, height: 1080 },
      { width: 1366, height: 768 },
      { width: 1536, height: 864 },
      { width: 1440, height: 900 },
      { width: 1280, height: 720 },
      { width: 1600, height: 900 }
    ];

    const timezones = [
      'America/New_York',
      'America/Los_Angeles',
      'Europe/London',
      'Europe/Berlin',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Australia/Sydney'
    ];

    const locales = [
      'en-US',
      'en-GB',
      'zh-CN',
      'ja-JP',
      'de-DE',
      'fr-FR',
      'es-ES'
    ];

    const platforms = ['Win32', 'MacIntel', 'Linux x86_64'];

    const randomUA = userAgents[Math.floor(Math.random() * userAgents.length)];
    const randomViewport = viewports[Math.floor(Math.random() * viewports.length)];

    this.fingerprintConfig = {
      userAgent: randomUA,
      viewport: randomViewport,
      timezone: timezones[Math.floor(Math.random() * timezones.length)],
      locale: locales[Math.floor(Math.random() * locales.length)],
      platform: platforms[Math.floor(Math.random() * platforms.length)],
      // Canvas指纹随机化
      canvasNoise: Math.random() * 0.1,
      // WebGL指纹随机化
      webglVendor: ['Intel Inc.', 'NVIDIA Corporation', 'AMD'][Math.floor(Math.random() * 3)],
      webglRenderer: this._getRandomRenderer(),
      // 字体指纹
      fonts: this._getRandomFonts(),
      // 硬件指纹
      hardwareConcurrency: [2, 4, 8, 12, 16][Math.floor(Math.random() * 5)],
      deviceMemory: [2, 4, 8, 16][Math.floor(Math.random() * 4)],
      // 网络指纹
      connectionType: ['4g', 'wifi', 'ethernet'][Math.floor(Math.random() * 3)],
      // 随机种子
      seed: crypto.randomBytes(16).toString('hex')
    };

    console.log('🎭 生成随机浏览器指纹:', {
      userAgent: this.fingerprintConfig.userAgent.substring(0, 50) + '...',
      viewport: this.fingerprintConfig.viewport,
      timezone: this.fingerprintConfig.timezone,
      locale: this.fingerprintConfig.locale
    });

    return this.fingerprintConfig;
  }

  _getRandomRenderer() {
    const renderers = [
      'ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)',
      'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
      'ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)',
      'Intel(R) UHD Graphics 630',
      'NVIDIA GeForce GTX 1060',
      'AMD Radeon RX 580'
    ];
    return renderers[Math.floor(Math.random() * renderers.length)];
  }

  _getRandomFonts() {
    const baseFonts = ['Arial', 'Times New Roman', 'Courier New', 'Helvetica'];
    const extraFonts = ['Calibri', 'Cambria', 'Georgia', 'Verdana', 'Tahoma', 'Comic Sans MS'];
    const randomExtra = extraFonts.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 4) + 2);
    return [...baseFonts, ...randomExtra];
  }

  /**
   * 初始化简单回放器（带反反爬虫指纹伪装）
   */
  async initialize(options = {}) {
    console.log('🚀 初始化基于patch的简单回放器（带反反爬虫）...');

    // 生成或使用指定的指纹配置
    const fingerprint = options.fingerprint || this.generateRandomFingerprint();

    // 启动浏览器（添加反检测参数）
    this.browser = await chromium.launch({
      headless: false,
      args: [
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-client-side-phishing-detection',
        '--disable-sync',
        '--metrics-recording-only',
        '--no-report-upload',
        '--disable-dev-shm-usage',
        '--disable-extensions-except',
        '--disable-extensions',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--mute-audio',
        '--no-zygote',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-restore-session-state',
        '--disable-translate',
        '--disable-ipc-flooding-protection'
      ],
      ...options.launchOptions
    });

    // 创建上下文（使用指纹配置）
    this.context = await this.browser.newContext({
      userAgent: fingerprint.userAgent,
      viewport: fingerprint.viewport,
      locale: fingerprint.locale,
      timezoneId: fingerprint.timezone,
      permissions: ['geolocation', 'notifications'],
      geolocation: { latitude: 40.7128 + (Math.random() - 0.5) * 0.1, longitude: -74.0060 + (Math.random() - 0.5) * 0.1 },
      ...options.contextOptions
    });

    // 创建页面并注册到pageAliases
    const page = await this.context.newPage();
    this.pageAliases.set(page, 'page');

    // 应用高级反检测脚本
    await this._applyAntiDetectionScripts(page, fingerprint);

    // 检查patch是否可用
    if (!global.playwrightReplayAPI) {
      throw new Error('❌ 未找到patch暴露的API，请确保已应用playwright-core patch');
    }

    console.log('✅ 基于patch的简单回放器初始化完成');
    console.log('🔧 可用的patch API:', Object.keys(global.playwrightReplayAPI));
    console.log('🎭 已应用浏览器指纹伪装');

    return page;
  }

  /**
   * 应用高级反检测脚本
   * @private
   */
  async _applyAntiDetectionScripts(page, fingerprint) {
    console.log('🛡️ 应用反检测脚本...');

    // 在页面加载前注入脚本
    await page.addInitScript((fingerprintData) => {
      // 1. 移除webdriver标识
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // 2. 伪装Chrome运行时
      window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {},
        app: {}
      };

      // 3. 伪装插件
      Object.defineProperty(navigator, 'plugins', {
        get: () => [
          {
            0: { type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format" },
            description: "Portable Document Format",
            filename: "internal-pdf-viewer",
            length: 1,
            name: "Chrome PDF Plugin"
          },
          {
            0: { type: "application/pdf", suffixes: "pdf", description: "" },
            description: "",
            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
            length: 1,
            name: "Chrome PDF Viewer"
          }
        ],
      });

      // 4. 伪装语言
      Object.defineProperty(navigator, 'languages', {
        get: () => [fingerprintData.locale, 'en'],
      });

      // 5. 伪装平台
      Object.defineProperty(navigator, 'platform', {
        get: () => fingerprintData.platform,
      });

      // 6. 伪装硬件信息
      Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => fingerprintData.hardwareConcurrency,
      });

      if ('deviceMemory' in navigator) {
        Object.defineProperty(navigator, 'deviceMemory', {
          get: () => fingerprintData.deviceMemory,
        });
      }

      // 7. Canvas指纹随机化
      const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;

      HTMLCanvasElement.prototype.toDataURL = function(type) {
        const shift = fingerprintData.canvasNoise;
        const originalMethod = originalToDataURL.apply(this, arguments);

        if (type === 'image/png' || !type) {
          // 添加微小的噪声
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = this.width;
          canvas.height = this.height;

          const img = new Image();
          img.onload = function() {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            for (let i = 0; i < imageData.data.length; i += 4) {
              imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(shift * 10));
            }
            ctx.putImageData(imageData, 0, 0);
          };
          img.src = originalMethod;
          return canvas.toDataURL(type);
        }
        return originalMethod;
      };

      // 8. WebGL指纹伪装
      const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
          return fingerprintData.webglVendor;
        }
        if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
          return fingerprintData.webglRenderer;
        }
        return originalGetParameter.apply(this, arguments);
      };

      // 9. 字体检测对抗
      const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');

      Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
        get: function() {
          const width = originalOffsetWidth.get.call(this);
          return width + (Math.random() - 0.5) * 0.1;
        }
      });

      // 10. 时区伪装已通过context设置

      // 11. 屏幕分辨率伪装
      Object.defineProperty(screen, 'width', {
        get: () => fingerprintData.viewport.width,
      });
      Object.defineProperty(screen, 'height', {
        get: () => fingerprintData.viewport.height,
      });
      Object.defineProperty(screen, 'availWidth', {
        get: () => fingerprintData.viewport.width,
      });
      Object.defineProperty(screen, 'availHeight', {
        get: () => fingerprintData.viewport.height - 40, // 减去任务栏高度
      });

      // 12. 网络连接信息伪装
      if ('connection' in navigator) {
        Object.defineProperty(navigator, 'connection', {
          get: () => ({
            effectiveType: fingerprintData.connectionType,
            rtt: 50 + Math.floor(Math.random() * 100),
            downlink: 10 + Math.random() * 40,
            saveData: false
          }),
        });
      }

      // 13. 电池API伪装
      if ('getBattery' in navigator) {
        navigator.getBattery = function() {
          return Promise.resolve({
            charging: Math.random() > 0.5,
            chargingTime: Math.floor(Math.random() * 3600),
            dischargingTime: Math.floor(Math.random() * 7200),
            level: 0.1 + Math.random() * 0.8
          });
        };
      }

      // 14. 权限API伪装
      if ('permissions' in navigator) {
        const originalQuery = navigator.permissions.query;
        navigator.permissions.query = function(parameters) {
          return originalQuery.apply(this, arguments).then(result => {
            if (parameters.name === 'notifications') {
              Object.defineProperty(result, 'state', { get: () => 'granted' });
            }
            return result;
          });
        };
      }

      console.log('🛡️ 反检测脚本已注入');
    }, fingerprint);

    // 设置额外的页面属性
    await page.setExtraHTTPHeaders({
      'Accept-Language': `${fingerprint.locale},en;q=0.9`,
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-User': '?1',
      'Cache-Control': 'max-age=0'
    });

    console.log('✅ 反检测脚本应用完成');
  }

  /**
   * 执行JSON文件回放
   */
  async executeJsonFile(jsonFilePath) {
    const fs = require('fs');
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
    const jsonData = JSON.parse(jsonContent);
    
    return await this.executeJsonData(jsonData);
  }

  /**
   * 执行JSON数据回放 - 使用patch中的全局API
   */
  async executeJsonData(jsonData) {
    console.log('🎬 开始使用patch API进行JSON回放...');
    
    // 解析动作列表
    const actions = Array.isArray(jsonData) ? jsonData : [jsonData];
    
    for (let i = 0; i < actions.length; i++) {
      const actionData = actions[i];
      console.log(`执行步骤 ${i + 1}/${actions.length}:`, actionData.name);
      
      try {
        // 🎯 关键：转换为官方的ActionInContext格式
        const actionInContext = this._convertToActionInContext(actionData);
        
        // 🎯 直接使用patch暴露的performAction
        await global.playwrightReplayAPI.performAction(this.pageAliases, actionInContext);
        
        console.log(`✅ 步骤 ${i + 1} 执行成功`);
        
        // 短暂等待，确保动作完成
        await this._getCurrentPage().waitForTimeout(100);
        
      } catch (error) {
        console.error(`❌ 步骤 ${i + 1} 执行失败:`, error.message);
        throw error;
      }
    }
    
    console.log('🎉 patch API JSON回放完成！');
  }

  /**
   * 批量执行动作序列 - 使用patch中的批量API
   */
  async executeBatchActions(jsonData) {
    console.log('🚀 开始批量执行动作序列...');
    
    const actions = Array.isArray(jsonData) ? jsonData : [jsonData];
    const actionInContexts = actions.map(actionData => this._convertToActionInContext(actionData));
    
    try {
      // 🎯 使用patch中的批量执行API
      const results = await global.playwrightReplayAPI.executeActionSequence(this.pageAliases, actionInContexts);
      
      console.log('🎉 批量执行完成！');
      return results;
      
    } catch (error) {
      console.error('❌ 批量执行失败:', error.message);
      throw error;
    }
  }

  /**
   * 转换JSON数据为官方ActionInContext格式
   * @private
   */
  _convertToActionInContext(actionData) {
    const actionInContext = {
      frame: {
        pageAlias: actionData.pageAlias || 'page',
        framePath: actionData.framePath || []
      },
      action: {
        name: actionData.name,
        signals: actionData.signals || []
      },
      startTime: Date.now()
    };

    // 根据动作类型添加特定属性
    switch (actionData.name) {
      case 'navigate':
        actionInContext.action.url = actionData.url;
        break;
        
      case 'click':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.button = actionData.button || 'left';
        actionInContext.action.modifiers = actionData.modifiers || 0;
        actionInContext.action.clickCount = actionData.clickCount || 1;
        if (actionData.position) {
          actionInContext.action.position = actionData.position;
        }
        break;
        
      case 'fill':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.text = actionData.text;
        break;
        
      case 'press':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.key = actionData.key;
        actionInContext.action.modifiers = actionData.modifiers || 0;
        break;
        
      case 'check':
      case 'uncheck':
        actionInContext.action.selector = actionData.selector;
        break;
        
      case 'select':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.options = actionData.options || [];
        break;
        
      case 'setInputFiles':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.files = actionData.files || [];
        break;
    }

    return actionInContext;
  }

  /**
   * 获取当前页面
   */
  _getCurrentPage() {
    return [...this.pageAliases.keys()][0];
  }

  /**
   * 设置自定义指纹配置
   */
  setCustomFingerprint(customConfig) {
    this.fingerprintConfig = {
      ...this.generateRandomFingerprint(),
      ...customConfig
    };
    console.log('🎭 设置自定义指纹配置');
    return this.fingerprintConfig;
  }

  /**
   * 获取当前指纹配置
   */
  getCurrentFingerprint() {
    return this.fingerprintConfig;
  }

  /**
   * 模拟人类行为的延迟
   */
  async humanDelay(min = 100, max = 300) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * 模拟鼠标移动轨迹
   */
  async simulateMouseMovement(page, selector) {
    try {
      const element = await page.locator(selector).first();
      const box = await element.boundingBox();

      if (box) {
        // 模拟从随机位置移动到目标元素
        const startX = Math.random() * 200;
        const startY = Math.random() * 200;
        const endX = box.x + box.width / 2;
        const endY = box.y + box.height / 2;

        // 分步移动
        const steps = 5;
        for (let i = 0; i <= steps; i++) {
          const x = startX + (endX - startX) * (i / steps);
          const y = startY + (endY - startY) * (i / steps);
          await page.mouse.move(x, y);
          await this.humanDelay(20, 50);
        }
      }
    } catch (error) {
      console.log('⚠️ 鼠标移动模拟失败:', error.message);
    }
  }

  /**
   * 检测页面是否有反爬虫机制
   */
  async detectAntiBot(page) {
    console.log('🔍 检测反爬虫机制...');

    const detections = [];

    try {
      // 检测常见的反爬虫服务
      const content = await page.content();

      if (content.includes('cloudflare') || content.includes('cf-ray')) {
        detections.push('Cloudflare');
      }

      if (content.includes('distil') || content.includes('distil_identifier')) {
        detections.push('Distil Networks');
      }

      if (content.includes('imperva') || content.includes('incapsula')) {
        detections.push('Imperva/Incapsula');
      }

      if (content.includes('recaptcha') || content.includes('g-recaptcha')) {
        detections.push('reCAPTCHA');
      }

      // 检测JavaScript挑战
      const hasJSChallenge = await page.evaluate(() => {
        return document.title.includes('Just a moment') ||
               document.body.innerText.includes('Checking your browser') ||
               document.body.innerText.includes('Please wait');
      });

      if (hasJSChallenge) {
        detections.push('JavaScript Challenge');
      }

      if (detections.length > 0) {
        console.log('🚨 检测到反爬虫机制:', detections.join(', '));
        return detections;
      } else {
        console.log('✅ 未检测到明显的反爬虫机制');
        return [];
      }

    } catch (error) {
      console.log('⚠️ 反爬虫检测失败:', error.message);
      return [];
    }
  }

  /**
   * 等待页面完全加载（包括异步内容）
   */
  async waitForPageLoad(page, timeout = 30000) {
    try {
      // 等待网络空闲
      await page.waitForLoadState('networkidle', { timeout });

      // 等待额外时间确保JavaScript执行完成
      await this.humanDelay(1000, 2000);

      console.log('✅ 页面加载完成');
    } catch (error) {
      console.log('⚠️ 页面加载超时，继续执行');
    }
  }

  /**
   * 获取patch API信息
   */
  getPatchInfo() {
    if (!global.playwrightReplayAPI) {
      return null;
    }

    return {
      version: global.playwrightReplayAPI.version,
      patchVersion: global.playwrightReplayAPI.patchVersion,
      isPatchedAPI: global.playwrightReplayAPI.isPatchedAPI,
      availableFunctions: Object.keys(global.playwrightReplayAPI)
    };
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.context) {
      await this.context.close();
    }
    if (this.browser) {
      await this.browser.close();
    }
    console.log('🧹 简单回放器已清理');
  }
}

// 使用示例
async function demonstrateSimpleReplay() {
  const executor = new SimplePatchBasedReplay();

  try {
    // 方式1：使用随机指纹
    console.log('\n🎭 方式1：使用随机指纹初始化');
    const page = await executor.initialize();

    // 显示指纹和patch信息
    const fingerprint = executor.getCurrentFingerprint();
    const patchInfo = executor.getPatchInfo();
    console.log('🎭 当前指纹:', {
      userAgent: fingerprint.userAgent.substring(0, 50) + '...',
      viewport: fingerprint.viewport,
      timezone: fingerprint.timezone,
      locale: fingerprint.locale
    });
    console.log('🔧 Patch信息:', patchInfo);

    // 示例JSON数据
    const sampleActions = [
      {
        name: 'navigate',
        url: 'https://httpbin.org/headers',
        pageAlias: 'page',
        framePath: []
      }
    ];

    // 执行导航并检测反爬虫
    await executor.executeJsonData(sampleActions);
    await executor.waitForPageLoad(page);

    // 检测反爬虫机制
    await executor.detectAntiBot(page);

    // 等待查看结果
    await executor.humanDelay(3000, 5000);

  } catch (error) {
    console.error('回放失败:', error);
  } finally {
    await executor.cleanup();
  }
}

// 高级使用示例：自定义指纹
async function demonstrateCustomFingerprint() {
  const executor = new SimplePatchBasedReplay();

  try {
    // 设置自定义指纹
    const customFingerprint = executor.setCustomFingerprint({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      timezone: 'America/New_York',
      locale: 'en-US',
      platform: 'Win32'
    });

    console.log('\n🎭 使用自定义指纹初始化');
    const page = await executor.initialize({ fingerprint: customFingerprint });

    // 测试指纹是否生效
    const actualUA = await page.evaluate(() => navigator.userAgent);
    const actualViewport = await page.evaluate(() => ({ width: screen.width, height: screen.height }));
    const actualTimezone = await page.evaluate(() => Intl.DateTimeFormat().resolvedOptions().timeZone);

    console.log('✅ 指纹验证:');
    console.log('  User Agent:', actualUA === customFingerprint.userAgent ? '✅' : '❌');
    console.log('  Viewport:', JSON.stringify(actualViewport) === JSON.stringify(customFingerprint.viewport) ? '✅' : '❌');
    console.log('  Timezone:', actualTimezone === customFingerprint.timezone ? '✅' : '❌');

    // 测试Canvas指纹
    const canvasFingerprint1 = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Canvas fingerprint test 🎭', 2, 2);
      return canvas.toDataURL();
    });

    // 刷新页面再次测试
    await page.reload();
    const canvasFingerprint2 = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Canvas fingerprint test 🎭', 2, 2);
      return canvas.toDataURL();
    });

    console.log('🎨 Canvas指纹随机化:', canvasFingerprint1 !== canvasFingerprint2 ? '✅' : '❌');

    await executor.humanDelay(3000, 5000);

  } catch (error) {
    console.error('自定义指纹测试失败:', error);
  } finally {
    await executor.cleanup();
  }
}

// 实战示例：处理有反爬虫保护的网站
async function demonstrateAntiDetection() {
  const executor = new SimplePatchBasedReplay();

  try {
    console.log('\n�️ 反反爬虫实战演示');
    const page = await executor.initialize();

    // 访问一个可能有反爬虫保护的网站
    const testActions = [
      {
        name: 'navigate',
        url: 'https://bot.sannysoft.com/',
        pageAlias: 'page',
        framePath: []
      }
    ];

    await executor.executeJsonData(testActions);
    await executor.waitForPageLoad(page);

    // 检测反爬虫机制
    await executor.detectAntiBot(page);

    // 检查webdriver检测结果
    const webdriverDetected = await page.evaluate(() => {
      const results = [];
      const webdriverElement = document.querySelector('tr:has(td:contains("webdriver"))');
      if (webdriverElement) {
        const result = webdriverElement.querySelector('td:last-child').textContent;
        results.push(`webdriver: ${result}`);
      }
      return results;
    });

    console.log('🔍 WebDriver检测结果:', webdriverDetected);

    await executor.humanDelay(5000, 8000);

  } catch (error) {
    console.error('反反爬虫演示失败:', error);
  } finally {
    await executor.cleanup();
  }
}

module.exports = {
  SimplePatchBasedReplay,
  demonstrateSimpleReplay,
  demonstrateCustomFingerprint,
  demonstrateAntiDetection
};

// 如果直接运行此文件，执行演示
if (require.main === module) {
  console.log('🎭 Playwright反反爬虫回放器演示\n');
  console.log('选择演示模式:');
  console.log('1. 基础演示 - demonstrateSimpleReplay()');
  console.log('2. 自定义指纹 - demonstrateCustomFingerprint()');
  console.log('3. 反反爬虫实战 - demonstrateAntiDetection()');

  // 默认运行基础演示
  demonstrateSimpleReplay().catch(console.error);
}
