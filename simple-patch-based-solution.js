/**
 * 🎯 最简单的解决方案：基于你的patch的全局API
 * 
 * 直接使用你patch中暴露的global.playwrightReplayAPI
 * 优势：
 * 1. 零额外代码，直接使用patch
 * 2. 完全官方API，维护成本最低
 * 3. 已经过测试验证
 */

const { chromium } = require('playwright-core');

class SimplePatchBasedReplay {
  constructor() {
    this.browser = null;
    this.context = null;
    this.pageAliases = new Map();
  }

  /**
   * 初始化简单回放器
   */
  async initialize(options = {}) {
    console.log('🚀 初始化基于patch的简单回放器...');
    
    // 启动浏览器
    this.browser = await chromium.launch({
      headless: false,
      ...options.launchOptions
    });
    
    // 创建上下文
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      ...options.contextOptions
    });
    
    // 创建页面并注册到pageAliases
    const page = await this.context.newPage();
    this.pageAliases.set(page, 'page');
    
    // 检查patch是否可用
    if (!global.playwrightReplayAPI) {
      throw new Error('❌ 未找到patch暴露的API，请确保已应用playwright-core patch');
    }
    
    console.log('✅ 基于patch的简单回放器初始化完成');
    console.log('🔧 可用的patch API:', Object.keys(global.playwrightReplayAPI));
    
    return page;
  }

  /**
   * 执行JSON文件回放
   */
  async executeJsonFile(jsonFilePath) {
    const fs = require('fs');
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
    const jsonData = JSON.parse(jsonContent);
    
    return await this.executeJsonData(jsonData);
  }

  /**
   * 执行JSON数据回放 - 使用patch中的全局API
   */
  async executeJsonData(jsonData) {
    console.log('🎬 开始使用patch API进行JSON回放...');
    
    // 解析动作列表
    const actions = Array.isArray(jsonData) ? jsonData : [jsonData];
    
    for (let i = 0; i < actions.length; i++) {
      const actionData = actions[i];
      console.log(`执行步骤 ${i + 1}/${actions.length}:`, actionData.name);
      
      try {
        // 🎯 关键：转换为官方的ActionInContext格式
        const actionInContext = this._convertToActionInContext(actionData);
        
        // 🎯 直接使用patch暴露的performAction
        await global.playwrightReplayAPI.performAction(this.pageAliases, actionInContext);
        
        console.log(`✅ 步骤 ${i + 1} 执行成功`);
        
        // 短暂等待，确保动作完成
        await this._getCurrentPage().waitForTimeout(100);
        
      } catch (error) {
        console.error(`❌ 步骤 ${i + 1} 执行失败:`, error.message);
        throw error;
      }
    }
    
    console.log('🎉 patch API JSON回放完成！');
  }

  /**
   * 批量执行动作序列 - 使用patch中的批量API
   */
  async executeBatchActions(jsonData) {
    console.log('🚀 开始批量执行动作序列...');
    
    const actions = Array.isArray(jsonData) ? jsonData : [jsonData];
    const actionInContexts = actions.map(actionData => this._convertToActionInContext(actionData));
    
    try {
      // 🎯 使用patch中的批量执行API
      const results = await global.playwrightReplayAPI.executeActionSequence(this.pageAliases, actionInContexts);
      
      console.log('🎉 批量执行完成！');
      return results;
      
    } catch (error) {
      console.error('❌ 批量执行失败:', error.message);
      throw error;
    }
  }

  /**
   * 转换JSON数据为官方ActionInContext格式
   * @private
   */
  _convertToActionInContext(actionData) {
    const actionInContext = {
      frame: {
        pageAlias: actionData.pageAlias || 'page',
        framePath: actionData.framePath || []
      },
      action: {
        name: actionData.name,
        signals: actionData.signals || []
      },
      startTime: Date.now()
    };

    // 根据动作类型添加特定属性
    switch (actionData.name) {
      case 'navigate':
        actionInContext.action.url = actionData.url;
        break;
        
      case 'click':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.button = actionData.button || 'left';
        actionInContext.action.modifiers = actionData.modifiers || 0;
        actionInContext.action.clickCount = actionData.clickCount || 1;
        if (actionData.position) {
          actionInContext.action.position = actionData.position;
        }
        break;
        
      case 'fill':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.text = actionData.text;
        break;
        
      case 'press':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.key = actionData.key;
        actionInContext.action.modifiers = actionData.modifiers || 0;
        break;
        
      case 'check':
      case 'uncheck':
        actionInContext.action.selector = actionData.selector;
        break;
        
      case 'select':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.options = actionData.options || [];
        break;
        
      case 'setInputFiles':
        actionInContext.action.selector = actionData.selector;
        actionInContext.action.files = actionData.files || [];
        break;
    }

    return actionInContext;
  }

  /**
   * 获取当前页面
   */
  _getCurrentPage() {
    return [...this.pageAliases.keys()][0];
  }

  /**
   * 获取patch API信息
   */
  getPatchInfo() {
    if (!global.playwrightReplayAPI) {
      return null;
    }
    
    return {
      version: global.playwrightReplayAPI.version,
      patchVersion: global.playwrightReplayAPI.patchVersion,
      isPatchedAPI: global.playwrightReplayAPI.isPatchedAPI,
      availableFunctions: Object.keys(global.playwrightReplayAPI)
    };
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.context) {
      await this.context.close();
    }
    if (this.browser) {
      await this.browser.close();
    }
    console.log('🧹 简单回放器已清理');
  }
}

// 使用示例
async function demonstrateSimpleReplay() {
  const executor = new SimplePatchBasedReplay();
  
  try {
    // 初始化
    const page = await executor.initialize();
    
    // 显示patch信息
    const patchInfo = executor.getPatchInfo();
    console.log('🔧 Patch信息:', patchInfo);
    
    // 示例JSON数据
    const sampleActions = [
      {
        name: 'navigate',
        url: 'https://example.com',
        pageAlias: 'page',
        framePath: []
      },
      {
        name: 'click',
        selector: 'h1',
        button: 'left',
        modifiers: 0,
        clickCount: 1,
        pageAlias: 'page',
        framePath: []
      }
    ];
    
    // 方式1：逐个执行
    console.log('\n📝 方式1：逐个执行动作');
    await executor.executeJsonData(sampleActions);
    
    // 方式2：批量执行（如果patch支持）
    if (global.playwrightReplayAPI.executeActionSequence) {
      console.log('\n📝 方式2：批量执行动作');
      await executor.executeBatchActions(sampleActions);
    }
    
    // 等待查看结果
    await page.waitForTimeout(3000);
    
  } catch (error) {
    console.error('回放失败:', error);
  } finally {
    await executor.cleanup();
  }
}

module.exports = { SimplePatchBasedReplay };

// 如果直接运行此文件，执行演示
if (require.main === module) {
  demonstrateSimpleReplay().catch(console.error);
}
