{"name": "@playwright/client", "private": true, "version": "0.0.0", "description": "A thin client for Playwright", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./package.json": "./package.json"}, "scripts": {"esbuild": "node build.js", "build": "npm run esbuild", "watch": "npm run esbuild -- --watch"}, "dependencies": {"playwright-core": "1.54.1"}}