<!--
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/playwright-logo.svg" type="image/svg+xml">
    <link rel="manifest" href="/manifest.webmanifest">
    <title>Playwright Trace Viewer</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
    <dialog id="fallback-error">
      <p>The Playwright Trace Viewer must be loaded over the <code>http://</code> or <code>https://</code> protocols.</p>
      <p>For more information, please see the <a href="https://aka.ms/playwright/trace-viewer-file-protocol">docs</a>.</p>
    </dialog>
    <script>
      if (!/^https?:/.test(window.location.protocol)) {
        const fallbackErrorDialog = document.getElementById('fallback-error');
        const isTraceViewerInsidePlaywrightReport = window.location.protocol === 'file:' && window.location.pathname.endsWith('/trace/index.html');
        // Best-effort to show the report path in the dialog.
        if (isTraceViewerInsidePlaywrightReport) {
          const reportPath = (() => {
            const base = window.location.pathname.replace(/\/trace\/index\.html$/, '');
            if (navigator.platform === 'Win32')
              return base.replace(/^\//, '').replace(/\//g, '\\\\');
            return base;
          })();
          const reportLink = document.createElement('div');
          const command = `npx playwright show-report ${reportPath}`;
          reportLink.innerHTML = `You can open the report via <code>${command}</code> from your Playwright project. <button type="button">Copy Command</button>`;
          fallbackErrorDialog.insertBefore(reportLink, fallbackErrorDialog.children[1]);
          reportLink.querySelector('button').addEventListener('click', () => navigator.clipboard.writeText(command));
        }
        fallbackErrorDialog.show();
      }
    </script>
  </body>
</html>
