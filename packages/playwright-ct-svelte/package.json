{"name": "@playwright/experimental-ct-svelte", "version": "1.54.1", "description": "Playwright Component Testing for Svelte", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./register": {"types": "./register.d.ts", "default": "./register.mjs"}, "./hooks": {"types": "./hooks.d.ts", "default": "./hooks.mjs"}, "./package.json": "./package.json"}, "dependencies": {"@playwright/experimental-ct-core": "1.54.1", "@sveltejs/vite-plugin-svelte": "^3.0.1"}, "devDependencies": {"svelte": "^4.2.19"}, "bin": {"playwright": "cli.js"}}