{"name": "@playwright/browser-firefox", "version": "1.54.1", "description": "Playwright package that automatically installs Firefox", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./package.json": "./package.json"}, "scripts": {"install": "node install.js"}, "dependencies": {"playwright-core": "1.54.1"}}