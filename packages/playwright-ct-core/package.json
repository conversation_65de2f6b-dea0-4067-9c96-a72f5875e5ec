{"name": "@playwright/experimental-ct-core", "version": "1.54.1", "description": "Playwright Component Testing Helpers", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./lib/mount": "./lib/mount.js", "./lib/program": "./lib/program.js", "./types/component": {"types": "./types/component.d.ts"}}, "dependencies": {"playwright-core": "1.54.1", "vite": "^6.3.4", "playwright": "1.54.1"}}