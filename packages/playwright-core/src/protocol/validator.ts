/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This file is generated by generate_channels.js, do not edit manually.

import { scheme, tOptional, tObject, tBoolean, tNumber, tString, tAny, tEnum, tArray, tBinary, tChannel, tType } from './validatorPrimitives';
export type { Validator, ValidatorContext } from './validatorPrimitives';
export { ValidationError, findValidator, maybeFindValidator, createMetadataValidator } from './validatorPrimitives';

scheme.StackFrame = tObject({
  file: tString,
  line: tNumber,
  column: tNumber,
  function: tOptional(tString),
});
scheme.Metadata = tObject({
  location: tOptional(tObject({
    file: tString,
    line: tOptional(tNumber),
    column: tOptional(tNumber),
  })),
  title: tOptional(tString),
  internal: tOptional(tBoolean),
  stepId: tOptional(tString),
});
scheme.ClientSideCallMetadata = tObject({
  id: tNumber,
  stack: tOptional(tArray(tType('StackFrame'))),
});
scheme.Point = tObject({
  x: tNumber,
  y: tNumber,
});
scheme.Rect = tObject({
  x: tNumber,
  y: tNumber,
  width: tNumber,
  height: tNumber,
});
scheme.SerializedValue = tObject({
  n: tOptional(tNumber),
  b: tOptional(tBoolean),
  s: tOptional(tString),
  v: tOptional(tEnum(['null', 'undefined', 'NaN', 'Infinity', '-Infinity', '-0'])),
  d: tOptional(tString),
  u: tOptional(tString),
  bi: tOptional(tString),
  ta: tOptional(tObject({
    b: tBinary,
    k: tEnum(['i8', 'ui8', 'ui8c', 'i16', 'ui16', 'i32', 'ui32', 'f32', 'f64', 'bi64', 'bui64']),
  })),
  e: tOptional(tObject({
    m: tString,
    n: tString,
    s: tString,
  })),
  r: tOptional(tObject({
    p: tString,
    f: tString,
  })),
  a: tOptional(tArray(tType('SerializedValue'))),
  o: tOptional(tArray(tObject({
    k: tString,
    v: tType('SerializedValue'),
  }))),
  h: tOptional(tNumber),
  id: tOptional(tNumber),
  ref: tOptional(tNumber),
});
scheme.SerializedArgument = tObject({
  value: tType('SerializedValue'),
  handles: tArray(tChannel('*')),
});
scheme.ExpectedTextValue = tObject({
  string: tOptional(tString),
  regexSource: tOptional(tString),
  regexFlags: tOptional(tString),
  matchSubstring: tOptional(tBoolean),
  ignoreCase: tOptional(tBoolean),
  normalizeWhiteSpace: tOptional(tBoolean),
});
scheme.SelectorEngine = tObject({
  name: tString,
  source: tString,
  contentScript: tOptional(tBoolean),
});
scheme.AXNode = tObject({
  role: tString,
  name: tString,
  valueString: tOptional(tString),
  valueNumber: tOptional(tNumber),
  description: tOptional(tString),
  keyshortcuts: tOptional(tString),
  roledescription: tOptional(tString),
  valuetext: tOptional(tString),
  disabled: tOptional(tBoolean),
  expanded: tOptional(tBoolean),
  focused: tOptional(tBoolean),
  modal: tOptional(tBoolean),
  multiline: tOptional(tBoolean),
  multiselectable: tOptional(tBoolean),
  readonly: tOptional(tBoolean),
  required: tOptional(tBoolean),
  selected: tOptional(tBoolean),
  checked: tOptional(tEnum(['checked', 'unchecked', 'mixed'])),
  pressed: tOptional(tEnum(['pressed', 'released', 'mixed'])),
  level: tOptional(tNumber),
  valuemin: tOptional(tNumber),
  valuemax: tOptional(tNumber),
  autocomplete: tOptional(tString),
  haspopup: tOptional(tString),
  invalid: tOptional(tString),
  orientation: tOptional(tString),
  children: tOptional(tArray(tType('AXNode'))),
});
scheme.SetNetworkCookie = tObject({
  name: tString,
  value: tString,
  url: tOptional(tString),
  domain: tOptional(tString),
  path: tOptional(tString),
  expires: tOptional(tNumber),
  httpOnly: tOptional(tBoolean),
  secure: tOptional(tBoolean),
  sameSite: tOptional(tEnum(['Strict', 'Lax', 'None'])),
  partitionKey: tOptional(tString),
  _crHasCrossSiteAncestor: tOptional(tBoolean),
});
scheme.NetworkCookie = tObject({
  name: tString,
  value: tString,
  domain: tString,
  path: tString,
  expires: tNumber,
  httpOnly: tBoolean,
  secure: tBoolean,
  sameSite: tEnum(['Strict', 'Lax', 'None']),
  partitionKey: tOptional(tString),
  _crHasCrossSiteAncestor: tOptional(tBoolean),
});
scheme.NameValue = tObject({
  name: tString,
  value: tString,
});
scheme.IndexedDBDatabase = tObject({
  name: tString,
  version: tNumber,
  stores: tArray(tObject({
    name: tString,
    autoIncrement: tBoolean,
    keyPath: tOptional(tString),
    keyPathArray: tOptional(tArray(tString)),
    records: tArray(tObject({
      key: tOptional(tAny),
      keyEncoded: tOptional(tAny),
      value: tOptional(tAny),
      valueEncoded: tOptional(tAny),
    })),
    indexes: tArray(tObject({
      name: tString,
      keyPath: tOptional(tString),
      keyPathArray: tOptional(tArray(tString)),
      multiEntry: tBoolean,
      unique: tBoolean,
    })),
  })),
});
scheme.SetOriginStorage = tObject({
  origin: tString,
  localStorage: tArray(tType('NameValue')),
  indexedDB: tOptional(tArray(tType('IndexedDBDatabase'))),
});
scheme.OriginStorage = tObject({
  origin: tString,
  localStorage: tArray(tType('NameValue')),
  indexedDB: tOptional(tArray(tType('IndexedDBDatabase'))),
});
scheme.SerializedError = tObject({
  error: tOptional(tObject({
    message: tString,
    name: tString,
    stack: tOptional(tString),
  })),
  value: tOptional(tType('SerializedValue')),
});
scheme.RecordHarOptions = tObject({
  zip: tOptional(tBoolean),
  content: tOptional(tEnum(['embed', 'attach', 'omit'])),
  mode: tOptional(tEnum(['full', 'minimal'])),
  urlGlob: tOptional(tString),
  urlRegexSource: tOptional(tString),
  urlRegexFlags: tOptional(tString),
});
scheme.FormField = tObject({
  name: tString,
  value: tOptional(tString),
  file: tOptional(tObject({
    name: tString,
    mimeType: tOptional(tString),
    buffer: tBinary,
  })),
});
scheme.APIRequestContextInitializer = tObject({
  tracing: tChannel(['Tracing']),
});
scheme.APIRequestContextFetchParams = tObject({
  url: tString,
  encodedParams: tOptional(tString),
  params: tOptional(tArray(tType('NameValue'))),
  method: tOptional(tString),
  headers: tOptional(tArray(tType('NameValue'))),
  postData: tOptional(tBinary),
  jsonData: tOptional(tString),
  formData: tOptional(tArray(tType('NameValue'))),
  multipartData: tOptional(tArray(tType('FormField'))),
  timeout: tNumber,
  failOnStatusCode: tOptional(tBoolean),
  ignoreHTTPSErrors: tOptional(tBoolean),
  maxRedirects: tOptional(tNumber),
  maxRetries: tOptional(tNumber),
});
scheme.APIRequestContextFetchResult = tObject({
  response: tType('APIResponse'),
});
scheme.APIRequestContextFetchResponseBodyParams = tObject({
  fetchUid: tString,
});
scheme.APIRequestContextFetchResponseBodyResult = tObject({
  binary: tOptional(tBinary),
});
scheme.APIRequestContextFetchLogParams = tObject({
  fetchUid: tString,
});
scheme.APIRequestContextFetchLogResult = tObject({
  log: tArray(tString),
});
scheme.APIRequestContextStorageStateParams = tObject({
  indexedDB: tOptional(tBoolean),
});
scheme.APIRequestContextStorageStateResult = tObject({
  cookies: tArray(tType('NetworkCookie')),
  origins: tArray(tType('OriginStorage')),
});
scheme.APIRequestContextDisposeAPIResponseParams = tObject({
  fetchUid: tString,
});
scheme.APIRequestContextDisposeAPIResponseResult = tOptional(tObject({}));
scheme.APIRequestContextDisposeParams = tObject({
  reason: tOptional(tString),
});
scheme.APIRequestContextDisposeResult = tOptional(tObject({}));
scheme.APIResponse = tObject({
  fetchUid: tString,
  url: tString,
  status: tNumber,
  statusText: tString,
  headers: tArray(tType('NameValue')),
});
scheme.LifecycleEvent = tEnum(['load', 'domcontentloaded', 'networkidle', 'commit']);
scheme.LocalUtilsInitializer = tObject({
  deviceDescriptors: tArray(tObject({
    name: tString,
    descriptor: tObject({
      userAgent: tString,
      viewport: tObject({
        width: tNumber,
        height: tNumber,
      }),
      screen: tOptional(tObject({
        width: tNumber,
        height: tNumber,
      })),
      deviceScaleFactor: tNumber,
      isMobile: tBoolean,
      hasTouch: tBoolean,
      defaultBrowserType: tEnum(['chromium', 'firefox', 'webkit']),
    }),
  })),
});
scheme.LocalUtilsZipParams = tObject({
  zipFile: tString,
  entries: tArray(tType('NameValue')),
  stacksId: tOptional(tString),
  mode: tEnum(['write', 'append']),
  includeSources: tBoolean,
});
scheme.LocalUtilsZipResult = tOptional(tObject({}));
scheme.LocalUtilsHarOpenParams = tObject({
  file: tString,
});
scheme.LocalUtilsHarOpenResult = tObject({
  harId: tOptional(tString),
  error: tOptional(tString),
});
scheme.LocalUtilsHarLookupParams = tObject({
  harId: tString,
  url: tString,
  method: tString,
  headers: tArray(tType('NameValue')),
  postData: tOptional(tBinary),
  isNavigationRequest: tBoolean,
});
scheme.LocalUtilsHarLookupResult = tObject({
  action: tEnum(['error', 'redirect', 'fulfill', 'noentry']),
  message: tOptional(tString),
  redirectURL: tOptional(tString),
  status: tOptional(tNumber),
  headers: tOptional(tArray(tType('NameValue'))),
  body: tOptional(tBinary),
});
scheme.LocalUtilsHarCloseParams = tObject({
  harId: tString,
});
scheme.LocalUtilsHarCloseResult = tOptional(tObject({}));
scheme.LocalUtilsHarUnzipParams = tObject({
  zipFile: tString,
  harFile: tString,
});
scheme.LocalUtilsHarUnzipResult = tOptional(tObject({}));
scheme.LocalUtilsConnectParams = tObject({
  wsEndpoint: tString,
  headers: tOptional(tAny),
  exposeNetwork: tOptional(tString),
  slowMo: tOptional(tNumber),
  timeout: tNumber,
  socksProxyRedirectPortForTest: tOptional(tNumber),
});
scheme.LocalUtilsConnectResult = tObject({
  pipe: tChannel(['JsonPipe']),
  headers: tArray(tType('NameValue')),
});
scheme.LocalUtilsTracingStartedParams = tObject({
  tracesDir: tOptional(tString),
  traceName: tString,
});
scheme.LocalUtilsTracingStartedResult = tObject({
  stacksId: tString,
});
scheme.LocalUtilsAddStackToTracingNoReplyParams = tObject({
  callData: tType('ClientSideCallMetadata'),
});
scheme.LocalUtilsAddStackToTracingNoReplyResult = tOptional(tObject({}));
scheme.LocalUtilsTraceDiscardedParams = tObject({
  stacksId: tString,
});
scheme.LocalUtilsTraceDiscardedResult = tOptional(tObject({}));
scheme.LocalUtilsGlobToRegexParams = tObject({
  glob: tString,
  baseURL: tOptional(tString),
  webSocketUrl: tOptional(tBoolean),
});
scheme.LocalUtilsGlobToRegexResult = tObject({
  regex: tString,
});
scheme.RootInitializer = tOptional(tObject({}));
scheme.RootInitializeParams = tObject({
  sdkLanguage: tEnum(['javascript', 'python', 'java', 'csharp']),
});
scheme.RootInitializeResult = tObject({
  playwright: tChannel(['Playwright']),
});
scheme.PlaywrightInitializer = tObject({
  chromium: tChannel(['BrowserType']),
  firefox: tChannel(['BrowserType']),
  webkit: tChannel(['BrowserType']),
  _bidiChromium: tChannel(['BrowserType']),
  _bidiFirefox: tChannel(['BrowserType']),
  android: tChannel(['Android']),
  electron: tChannel(['Electron']),
  utils: tOptional(tChannel(['LocalUtils'])),
  preLaunchedBrowser: tOptional(tChannel(['Browser'])),
  preConnectedAndroidDevice: tOptional(tChannel(['AndroidDevice'])),
  socksSupport: tOptional(tChannel(['SocksSupport'])),
});
scheme.PlaywrightNewRequestParams = tObject({
  baseURL: tOptional(tString),
  userAgent: tOptional(tString),
  ignoreHTTPSErrors: tOptional(tBoolean),
  extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
  failOnStatusCode: tOptional(tBoolean),
  clientCertificates: tOptional(tArray(tObject({
    origin: tString,
    cert: tOptional(tBinary),
    key: tOptional(tBinary),
    passphrase: tOptional(tString),
    pfx: tOptional(tBinary),
  }))),
  maxRedirects: tOptional(tNumber),
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
    send: tOptional(tEnum(['always', 'unauthorized'])),
  })),
  proxy: tOptional(tObject({
    server: tString,
    bypass: tOptional(tString),
    username: tOptional(tString),
    password: tOptional(tString),
  })),
  storageState: tOptional(tObject({
    cookies: tOptional(tArray(tType('NetworkCookie'))),
    origins: tOptional(tArray(tType('SetOriginStorage'))),
  })),
  tracesDir: tOptional(tString),
});
scheme.PlaywrightNewRequestResult = tObject({
  request: tChannel(['APIRequestContext']),
});
scheme.RecorderSource = tObject({
  isRecorded: tBoolean,
  id: tString,
  label: tString,
  text: tString,
  language: tString,
  highlight: tArray(tObject({
    line: tNumber,
    type: tString,
  })),
  revealLine: tOptional(tNumber),
  group: tOptional(tString),
});
scheme.DebugControllerInitializer = tOptional(tObject({}));
scheme.DebugControllerInspectRequestedEvent = tObject({
  selector: tString,
  locator: tString,
  ariaSnapshot: tString,
});
scheme.DebugControllerSetModeRequestedEvent = tObject({
  mode: tString,
});
scheme.DebugControllerStateChangedEvent = tObject({
  pageCount: tNumber,
});
scheme.DebugControllerSourceChangedEvent = tObject({
  text: tString,
  header: tOptional(tString),
  footer: tOptional(tString),
  actions: tOptional(tArray(tString)),
});
scheme.DebugControllerPausedEvent = tObject({
  paused: tBoolean,
});
scheme.DebugControllerInitializeParams = tObject({
  codegenId: tString,
  sdkLanguage: tEnum(['javascript', 'python', 'java', 'csharp']),
});
scheme.DebugControllerInitializeResult = tOptional(tObject({}));
scheme.DebugControllerSetReportStateChangedParams = tObject({
  enabled: tBoolean,
});
scheme.DebugControllerSetReportStateChangedResult = tOptional(tObject({}));
scheme.DebugControllerResetForReuseParams = tOptional(tObject({}));
scheme.DebugControllerResetForReuseResult = tOptional(tObject({}));
scheme.DebugControllerNavigateParams = tObject({
  url: tString,
});
scheme.DebugControllerNavigateResult = tOptional(tObject({}));
scheme.DebugControllerSetRecorderModeParams = tObject({
  mode: tEnum(['inspecting', 'recording', 'none']),
  testIdAttributeName: tOptional(tString),
});
scheme.DebugControllerSetRecorderModeResult = tOptional(tObject({}));
scheme.DebugControllerHighlightParams = tObject({
  selector: tOptional(tString),
  ariaTemplate: tOptional(tString),
});
scheme.DebugControllerHighlightResult = tOptional(tObject({}));
scheme.DebugControllerHideHighlightParams = tOptional(tObject({}));
scheme.DebugControllerHideHighlightResult = tOptional(tObject({}));
scheme.DebugControllerResumeParams = tOptional(tObject({}));
scheme.DebugControllerResumeResult = tOptional(tObject({}));
scheme.DebugControllerKillParams = tOptional(tObject({}));
scheme.DebugControllerKillResult = tOptional(tObject({}));
scheme.DebugControllerCloseAllBrowsersParams = tOptional(tObject({}));
scheme.DebugControllerCloseAllBrowsersResult = tOptional(tObject({}));
scheme.SocksSupportInitializer = tOptional(tObject({}));
scheme.SocksSupportSocksRequestedEvent = tObject({
  uid: tString,
  host: tString,
  port: tNumber,
});
scheme.SocksSupportSocksDataEvent = tObject({
  uid: tString,
  data: tBinary,
});
scheme.SocksSupportSocksClosedEvent = tObject({
  uid: tString,
});
scheme.SocksSupportSocksConnectedParams = tObject({
  uid: tString,
  host: tString,
  port: tNumber,
});
scheme.SocksSupportSocksConnectedResult = tOptional(tObject({}));
scheme.SocksSupportSocksFailedParams = tObject({
  uid: tString,
  errorCode: tString,
});
scheme.SocksSupportSocksFailedResult = tOptional(tObject({}));
scheme.SocksSupportSocksDataParams = tObject({
  uid: tString,
  data: tBinary,
});
scheme.SocksSupportSocksDataResult = tOptional(tObject({}));
scheme.SocksSupportSocksErrorParams = tObject({
  uid: tString,
  error: tString,
});
scheme.SocksSupportSocksErrorResult = tOptional(tObject({}));
scheme.SocksSupportSocksEndParams = tObject({
  uid: tString,
});
scheme.SocksSupportSocksEndResult = tOptional(tObject({}));
scheme.BrowserTypeInitializer = tObject({
  executablePath: tString,
  name: tString,
});
scheme.BrowserTypeLaunchParams = tObject({
  channel: tOptional(tString),
  executablePath: tOptional(tString),
  args: tOptional(tArray(tString)),
  ignoreAllDefaultArgs: tOptional(tBoolean),
  ignoreDefaultArgs: tOptional(tArray(tString)),
  assistantMode: tOptional(tBoolean),
  handleSIGINT: tOptional(tBoolean),
  handleSIGTERM: tOptional(tBoolean),
  handleSIGHUP: tOptional(tBoolean),
  timeout: tNumber,
  env: tOptional(tArray(tType('NameValue'))),
  headless: tOptional(tBoolean),
  devtools: tOptional(tBoolean),
  proxy: tOptional(tObject({
    server: tString,
    bypass: tOptional(tString),
    username: tOptional(tString),
    password: tOptional(tString),
  })),
  downloadsPath: tOptional(tString),
  tracesDir: tOptional(tString),
  chromiumSandbox: tOptional(tBoolean),
  firefoxUserPrefs: tOptional(tAny),
  cdpPort: tOptional(tNumber),
  slowMo: tOptional(tNumber),
});
scheme.BrowserTypeLaunchResult = tObject({
  browser: tChannel(['Browser']),
});
scheme.BrowserTypeLaunchPersistentContextParams = tObject({
  channel: tOptional(tString),
  executablePath: tOptional(tString),
  args: tOptional(tArray(tString)),
  ignoreAllDefaultArgs: tOptional(tBoolean),
  ignoreDefaultArgs: tOptional(tArray(tString)),
  assistantMode: tOptional(tBoolean),
  handleSIGINT: tOptional(tBoolean),
  handleSIGTERM: tOptional(tBoolean),
  handleSIGHUP: tOptional(tBoolean),
  timeout: tNumber,
  env: tOptional(tArray(tType('NameValue'))),
  headless: tOptional(tBoolean),
  devtools: tOptional(tBoolean),
  proxy: tOptional(tObject({
    server: tString,
    bypass: tOptional(tString),
    username: tOptional(tString),
    password: tOptional(tString),
  })),
  downloadsPath: tOptional(tString),
  tracesDir: tOptional(tString),
  chromiumSandbox: tOptional(tBoolean),
  firefoxUserPrefs: tOptional(tAny),
  cdpPort: tOptional(tNumber),
  noDefaultViewport: tOptional(tBoolean),
  viewport: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  screen: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  ignoreHTTPSErrors: tOptional(tBoolean),
  clientCertificates: tOptional(tArray(tObject({
    origin: tString,
    cert: tOptional(tBinary),
    key: tOptional(tBinary),
    passphrase: tOptional(tString),
    pfx: tOptional(tBinary),
  }))),
  javaScriptEnabled: tOptional(tBoolean),
  bypassCSP: tOptional(tBoolean),
  userAgent: tOptional(tString),
  locale: tOptional(tString),
  timezoneId: tOptional(tString),
  geolocation: tOptional(tObject({
    longitude: tNumber,
    latitude: tNumber,
    accuracy: tOptional(tNumber),
  })),
  permissions: tOptional(tArray(tString)),
  extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
  offline: tOptional(tBoolean),
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
    send: tOptional(tEnum(['always', 'unauthorized'])),
  })),
  deviceScaleFactor: tOptional(tNumber),
  isMobile: tOptional(tBoolean),
  hasTouch: tOptional(tBoolean),
  colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: tOptional(tEnum(['reduce', 'no-preference', 'no-override'])),
  forcedColors: tOptional(tEnum(['active', 'none', 'no-override'])),
  acceptDownloads: tOptional(tEnum(['accept', 'deny', 'internal-browser-default'])),
  contrast: tOptional(tEnum(['no-preference', 'more', 'no-override'])),
  baseURL: tOptional(tString),
  recordVideo: tOptional(tObject({
    dir: tString,
    size: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
  })),
  strictSelectors: tOptional(tBoolean),
  serviceWorkers: tOptional(tEnum(['allow', 'block'])),
  selectorEngines: tOptional(tArray(tType('SelectorEngine'))),
  testIdAttributeName: tOptional(tString),
  userDataDir: tString,
  slowMo: tOptional(tNumber),
});
scheme.BrowserTypeLaunchPersistentContextResult = tObject({
  browser: tChannel(['Browser']),
  context: tChannel(['BrowserContext']),
});
scheme.BrowserTypeConnectOverCDPParams = tObject({
  endpointURL: tString,
  headers: tOptional(tArray(tType('NameValue'))),
  slowMo: tOptional(tNumber),
  timeout: tNumber,
});
scheme.BrowserTypeConnectOverCDPResult = tObject({
  browser: tChannel(['Browser']),
  defaultContext: tOptional(tChannel(['BrowserContext'])),
});
scheme.BrowserInitializer = tObject({
  version: tString,
  name: tString,
});
scheme.BrowserContextEvent = tObject({
  context: tChannel(['BrowserContext']),
});
scheme.BrowserCloseEvent = tOptional(tObject({}));
scheme.BrowserCloseParams = tObject({
  reason: tOptional(tString),
});
scheme.BrowserCloseResult = tOptional(tObject({}));
scheme.BrowserKillForTestsParams = tOptional(tObject({}));
scheme.BrowserKillForTestsResult = tOptional(tObject({}));
scheme.BrowserDefaultUserAgentForTestParams = tOptional(tObject({}));
scheme.BrowserDefaultUserAgentForTestResult = tObject({
  userAgent: tString,
});
scheme.BrowserNewContextParams = tObject({
  noDefaultViewport: tOptional(tBoolean),
  viewport: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  screen: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  ignoreHTTPSErrors: tOptional(tBoolean),
  clientCertificates: tOptional(tArray(tObject({
    origin: tString,
    cert: tOptional(tBinary),
    key: tOptional(tBinary),
    passphrase: tOptional(tString),
    pfx: tOptional(tBinary),
  }))),
  javaScriptEnabled: tOptional(tBoolean),
  bypassCSP: tOptional(tBoolean),
  userAgent: tOptional(tString),
  locale: tOptional(tString),
  timezoneId: tOptional(tString),
  geolocation: tOptional(tObject({
    longitude: tNumber,
    latitude: tNumber,
    accuracy: tOptional(tNumber),
  })),
  permissions: tOptional(tArray(tString)),
  extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
  offline: tOptional(tBoolean),
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
    send: tOptional(tEnum(['always', 'unauthorized'])),
  })),
  deviceScaleFactor: tOptional(tNumber),
  isMobile: tOptional(tBoolean),
  hasTouch: tOptional(tBoolean),
  colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: tOptional(tEnum(['reduce', 'no-preference', 'no-override'])),
  forcedColors: tOptional(tEnum(['active', 'none', 'no-override'])),
  acceptDownloads: tOptional(tEnum(['accept', 'deny', 'internal-browser-default'])),
  contrast: tOptional(tEnum(['no-preference', 'more', 'no-override'])),
  baseURL: tOptional(tString),
  recordVideo: tOptional(tObject({
    dir: tString,
    size: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
  })),
  strictSelectors: tOptional(tBoolean),
  serviceWorkers: tOptional(tEnum(['allow', 'block'])),
  selectorEngines: tOptional(tArray(tType('SelectorEngine'))),
  testIdAttributeName: tOptional(tString),
  proxy: tOptional(tObject({
    server: tString,
    bypass: tOptional(tString),
    username: tOptional(tString),
    password: tOptional(tString),
  })),
  storageState: tOptional(tObject({
    cookies: tOptional(tArray(tType('SetNetworkCookie'))),
    origins: tOptional(tArray(tType('SetOriginStorage'))),
  })),
});
scheme.BrowserNewContextResult = tObject({
  context: tChannel(['BrowserContext']),
});
scheme.BrowserNewContextForReuseParams = tObject({
  noDefaultViewport: tOptional(tBoolean),
  viewport: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  screen: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  ignoreHTTPSErrors: tOptional(tBoolean),
  clientCertificates: tOptional(tArray(tObject({
    origin: tString,
    cert: tOptional(tBinary),
    key: tOptional(tBinary),
    passphrase: tOptional(tString),
    pfx: tOptional(tBinary),
  }))),
  javaScriptEnabled: tOptional(tBoolean),
  bypassCSP: tOptional(tBoolean),
  userAgent: tOptional(tString),
  locale: tOptional(tString),
  timezoneId: tOptional(tString),
  geolocation: tOptional(tObject({
    longitude: tNumber,
    latitude: tNumber,
    accuracy: tOptional(tNumber),
  })),
  permissions: tOptional(tArray(tString)),
  extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
  offline: tOptional(tBoolean),
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
    send: tOptional(tEnum(['always', 'unauthorized'])),
  })),
  deviceScaleFactor: tOptional(tNumber),
  isMobile: tOptional(tBoolean),
  hasTouch: tOptional(tBoolean),
  colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: tOptional(tEnum(['reduce', 'no-preference', 'no-override'])),
  forcedColors: tOptional(tEnum(['active', 'none', 'no-override'])),
  acceptDownloads: tOptional(tEnum(['accept', 'deny', 'internal-browser-default'])),
  contrast: tOptional(tEnum(['no-preference', 'more', 'no-override'])),
  baseURL: tOptional(tString),
  recordVideo: tOptional(tObject({
    dir: tString,
    size: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
  })),
  strictSelectors: tOptional(tBoolean),
  serviceWorkers: tOptional(tEnum(['allow', 'block'])),
  selectorEngines: tOptional(tArray(tType('SelectorEngine'))),
  testIdAttributeName: tOptional(tString),
  proxy: tOptional(tObject({
    server: tString,
    bypass: tOptional(tString),
    username: tOptional(tString),
    password: tOptional(tString),
  })),
  storageState: tOptional(tObject({
    cookies: tOptional(tArray(tType('SetNetworkCookie'))),
    origins: tOptional(tArray(tType('SetOriginStorage'))),
  })),
});
scheme.BrowserNewContextForReuseResult = tObject({
  context: tChannel(['BrowserContext']),
});
scheme.BrowserDisconnectFromReusedContextParams = tObject({
  reason: tString,
});
scheme.BrowserDisconnectFromReusedContextResult = tOptional(tObject({}));
scheme.BrowserNewBrowserCDPSessionParams = tOptional(tObject({}));
scheme.BrowserNewBrowserCDPSessionResult = tObject({
  session: tChannel(['CDPSession']),
});
scheme.BrowserStartTracingParams = tObject({
  page: tOptional(tChannel(['Page'])),
  screenshots: tOptional(tBoolean),
  categories: tOptional(tArray(tString)),
});
scheme.BrowserStartTracingResult = tOptional(tObject({}));
scheme.BrowserStopTracingParams = tOptional(tObject({}));
scheme.BrowserStopTracingResult = tObject({
  artifact: tChannel(['Artifact']),
});
scheme.EventTargetInitializer = tOptional(tObject({}));
scheme.EventTargetWaitForEventInfoParams = tObject({
  info: tObject({
    waitId: tString,
    phase: tEnum(['before', 'after', 'log']),
    event: tOptional(tString),
    message: tOptional(tString),
    error: tOptional(tString),
  }),
});
scheme.BrowserContextWaitForEventInfoParams = tType('EventTargetWaitForEventInfoParams');
scheme.PageWaitForEventInfoParams = tType('EventTargetWaitForEventInfoParams');
scheme.WebSocketWaitForEventInfoParams = tType('EventTargetWaitForEventInfoParams');
scheme.ElectronApplicationWaitForEventInfoParams = tType('EventTargetWaitForEventInfoParams');
scheme.AndroidDeviceWaitForEventInfoParams = tType('EventTargetWaitForEventInfoParams');
scheme.EventTargetWaitForEventInfoResult = tOptional(tObject({}));
scheme.BrowserContextWaitForEventInfoResult = tType('EventTargetWaitForEventInfoResult');
scheme.PageWaitForEventInfoResult = tType('EventTargetWaitForEventInfoResult');
scheme.WebSocketWaitForEventInfoResult = tType('EventTargetWaitForEventInfoResult');
scheme.ElectronApplicationWaitForEventInfoResult = tType('EventTargetWaitForEventInfoResult');
scheme.AndroidDeviceWaitForEventInfoResult = tType('EventTargetWaitForEventInfoResult');
scheme.BrowserContextInitializer = tObject({
  isChromium: tBoolean,
  requestContext: tChannel(['APIRequestContext']),
  tracing: tChannel(['Tracing']),
  options: tObject({
    noDefaultViewport: tOptional(tBoolean),
    viewport: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
    screen: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
    ignoreHTTPSErrors: tOptional(tBoolean),
    clientCertificates: tOptional(tArray(tObject({
      origin: tString,
      cert: tOptional(tBinary),
      key: tOptional(tBinary),
      passphrase: tOptional(tString),
      pfx: tOptional(tBinary),
    }))),
    javaScriptEnabled: tOptional(tBoolean),
    bypassCSP: tOptional(tBoolean),
    userAgent: tOptional(tString),
    locale: tOptional(tString),
    timezoneId: tOptional(tString),
    geolocation: tOptional(tObject({
      longitude: tNumber,
      latitude: tNumber,
      accuracy: tOptional(tNumber),
    })),
    permissions: tOptional(tArray(tString)),
    extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
    offline: tOptional(tBoolean),
    httpCredentials: tOptional(tObject({
      username: tString,
      password: tString,
      origin: tOptional(tString),
      send: tOptional(tEnum(['always', 'unauthorized'])),
    })),
    deviceScaleFactor: tOptional(tNumber),
    isMobile: tOptional(tBoolean),
    hasTouch: tOptional(tBoolean),
    colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
    reducedMotion: tOptional(tEnum(['reduce', 'no-preference', 'no-override'])),
    forcedColors: tOptional(tEnum(['active', 'none', 'no-override'])),
    acceptDownloads: tOptional(tEnum(['accept', 'deny', 'internal-browser-default'])),
    contrast: tOptional(tEnum(['no-preference', 'more', 'no-override'])),
    baseURL: tOptional(tString),
    recordVideo: tOptional(tObject({
      dir: tString,
      size: tOptional(tObject({
        width: tNumber,
        height: tNumber,
      })),
    })),
    strictSelectors: tOptional(tBoolean),
    serviceWorkers: tOptional(tEnum(['allow', 'block'])),
    selectorEngines: tOptional(tArray(tType('SelectorEngine'))),
    testIdAttributeName: tOptional(tString),
  }),
});
scheme.BrowserContextBindingCallEvent = tObject({
  binding: tChannel(['BindingCall']),
});
scheme.BrowserContextConsoleEvent = tObject({
  type: tString,
  text: tString,
  args: tArray(tChannel(['ElementHandle', 'JSHandle'])),
  location: tObject({
    url: tString,
    lineNumber: tNumber,
    columnNumber: tNumber,
  }),
  page: tChannel(['Page']),
});
scheme.BrowserContextCloseEvent = tOptional(tObject({}));
scheme.BrowserContextDialogEvent = tObject({
  dialog: tChannel(['Dialog']),
});
scheme.BrowserContextPageEvent = tObject({
  page: tChannel(['Page']),
});
scheme.BrowserContextPageErrorEvent = tObject({
  error: tType('SerializedError'),
  page: tChannel(['Page']),
});
scheme.BrowserContextRouteEvent = tObject({
  route: tChannel(['Route']),
});
scheme.BrowserContextWebSocketRouteEvent = tObject({
  webSocketRoute: tChannel(['WebSocketRoute']),
});
scheme.BrowserContextVideoEvent = tObject({
  artifact: tChannel(['Artifact']),
});
scheme.BrowserContextBackgroundPageEvent = tObject({
  page: tChannel(['Page']),
});
scheme.BrowserContextServiceWorkerEvent = tObject({
  worker: tChannel(['Worker']),
});
scheme.BrowserContextRequestEvent = tObject({
  request: tChannel(['Request']),
  page: tOptional(tChannel(['Page'])),
});
scheme.BrowserContextRequestFailedEvent = tObject({
  request: tChannel(['Request']),
  failureText: tOptional(tString),
  responseEndTiming: tNumber,
  page: tOptional(tChannel(['Page'])),
});
scheme.BrowserContextRequestFinishedEvent = tObject({
  request: tChannel(['Request']),
  response: tOptional(tChannel(['Response'])),
  responseEndTiming: tNumber,
  page: tOptional(tChannel(['Page'])),
});
scheme.BrowserContextResponseEvent = tObject({
  response: tChannel(['Response']),
  page: tOptional(tChannel(['Page'])),
});
scheme.BrowserContextRecorderEventEvent = tObject({
  event: tEnum(['actionAdded', 'actionUpdated', 'signalAdded']),
  data: tAny,
  page: tChannel(['Page']),
});
scheme.BrowserContextAddCookiesParams = tObject({
  cookies: tArray(tType('SetNetworkCookie')),
});
scheme.BrowserContextAddCookiesResult = tOptional(tObject({}));
scheme.BrowserContextAddInitScriptParams = tObject({
  source: tString,
});
scheme.BrowserContextAddInitScriptResult = tOptional(tObject({}));
scheme.BrowserContextClearCookiesParams = tObject({
  name: tOptional(tString),
  nameRegexSource: tOptional(tString),
  nameRegexFlags: tOptional(tString),
  domain: tOptional(tString),
  domainRegexSource: tOptional(tString),
  domainRegexFlags: tOptional(tString),
  path: tOptional(tString),
  pathRegexSource: tOptional(tString),
  pathRegexFlags: tOptional(tString),
});
scheme.BrowserContextClearCookiesResult = tOptional(tObject({}));
scheme.BrowserContextClearPermissionsParams = tOptional(tObject({}));
scheme.BrowserContextClearPermissionsResult = tOptional(tObject({}));
scheme.BrowserContextCloseParams = tObject({
  reason: tOptional(tString),
});
scheme.BrowserContextCloseResult = tOptional(tObject({}));
scheme.BrowserContextCookiesParams = tObject({
  urls: tArray(tString),
});
scheme.BrowserContextCookiesResult = tObject({
  cookies: tArray(tType('NetworkCookie')),
});
scheme.BrowserContextExposeBindingParams = tObject({
  name: tString,
  needsHandle: tOptional(tBoolean),
});
scheme.BrowserContextExposeBindingResult = tOptional(tObject({}));
scheme.BrowserContextGrantPermissionsParams = tObject({
  permissions: tArray(tString),
  origin: tOptional(tString),
});
scheme.BrowserContextGrantPermissionsResult = tOptional(tObject({}));
scheme.BrowserContextNewPageParams = tOptional(tObject({}));
scheme.BrowserContextNewPageResult = tObject({
  page: tChannel(['Page']),
});
scheme.BrowserContextRegisterSelectorEngineParams = tObject({
  selectorEngine: tType('SelectorEngine'),
});
scheme.BrowserContextRegisterSelectorEngineResult = tOptional(tObject({}));
scheme.BrowserContextSetTestIdAttributeNameParams = tObject({
  testIdAttributeName: tString,
});
scheme.BrowserContextSetTestIdAttributeNameResult = tOptional(tObject({}));
scheme.BrowserContextSetExtraHTTPHeadersParams = tObject({
  headers: tArray(tType('NameValue')),
});
scheme.BrowserContextSetExtraHTTPHeadersResult = tOptional(tObject({}));
scheme.BrowserContextSetGeolocationParams = tObject({
  geolocation: tOptional(tObject({
    longitude: tNumber,
    latitude: tNumber,
    accuracy: tOptional(tNumber),
  })),
});
scheme.BrowserContextSetGeolocationResult = tOptional(tObject({}));
scheme.BrowserContextSetHTTPCredentialsParams = tObject({
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
  })),
});
scheme.BrowserContextSetHTTPCredentialsResult = tOptional(tObject({}));
scheme.BrowserContextSetNetworkInterceptionPatternsParams = tObject({
  patterns: tArray(tObject({
    glob: tOptional(tString),
    regexSource: tOptional(tString),
    regexFlags: tOptional(tString),
  })),
});
scheme.BrowserContextSetNetworkInterceptionPatternsResult = tOptional(tObject({}));
scheme.BrowserContextSetWebSocketInterceptionPatternsParams = tObject({
  patterns: tArray(tObject({
    glob: tOptional(tString),
    regexSource: tOptional(tString),
    regexFlags: tOptional(tString),
  })),
});
scheme.BrowserContextSetWebSocketInterceptionPatternsResult = tOptional(tObject({}));
scheme.BrowserContextSetOfflineParams = tObject({
  offline: tBoolean,
});
scheme.BrowserContextSetOfflineResult = tOptional(tObject({}));
scheme.BrowserContextStorageStateParams = tObject({
  indexedDB: tOptional(tBoolean),
});
scheme.BrowserContextStorageStateResult = tObject({
  cookies: tArray(tType('NetworkCookie')),
  origins: tArray(tType('OriginStorage')),
});
scheme.BrowserContextPauseParams = tOptional(tObject({}));
scheme.BrowserContextPauseResult = tOptional(tObject({}));
scheme.BrowserContextEnableRecorderParams = tObject({
  language: tOptional(tString),
  mode: tOptional(tEnum(['inspecting', 'recording'])),
  recorderMode: tOptional(tEnum(['default', 'api'])),
  pauseOnNextStatement: tOptional(tBoolean),
  testIdAttributeName: tOptional(tString),
  launchOptions: tOptional(tAny),
  contextOptions: tOptional(tAny),
  device: tOptional(tString),
  saveStorage: tOptional(tString),
  outputFile: tOptional(tString),
  handleSIGINT: tOptional(tBoolean),
  omitCallTracking: tOptional(tBoolean),
});
scheme.BrowserContextEnableRecorderResult = tOptional(tObject({}));
scheme.BrowserContextDisableRecorderParams = tOptional(tObject({}));
scheme.BrowserContextDisableRecorderResult = tOptional(tObject({}));
scheme.BrowserContextNewCDPSessionParams = tObject({
  page: tOptional(tChannel(['Page'])),
  frame: tOptional(tChannel(['Frame'])),
});
scheme.BrowserContextNewCDPSessionResult = tObject({
  session: tChannel(['CDPSession']),
});
scheme.BrowserContextHarStartParams = tObject({
  page: tOptional(tChannel(['Page'])),
  options: tType('RecordHarOptions'),
});
scheme.BrowserContextHarStartResult = tObject({
  harId: tString,
});
scheme.BrowserContextHarExportParams = tObject({
  harId: tOptional(tString),
});
scheme.BrowserContextHarExportResult = tObject({
  artifact: tChannel(['Artifact']),
});
scheme.BrowserContextCreateTempFilesParams = tObject({
  rootDirName: tOptional(tString),
  items: tArray(tObject({
    name: tString,
    lastModifiedMs: tOptional(tNumber),
  })),
});
scheme.BrowserContextCreateTempFilesResult = tObject({
  rootDir: tOptional(tChannel(['WritableStream'])),
  writableStreams: tArray(tChannel(['WritableStream'])),
});
scheme.BrowserContextUpdateSubscriptionParams = tObject({
  event: tEnum(['console', 'dialog', 'request', 'response', 'requestFinished', 'requestFailed']),
  enabled: tBoolean,
});
scheme.BrowserContextUpdateSubscriptionResult = tOptional(tObject({}));
scheme.BrowserContextClockFastForwardParams = tObject({
  ticksNumber: tOptional(tNumber),
  ticksString: tOptional(tString),
});
scheme.BrowserContextClockFastForwardResult = tOptional(tObject({}));
scheme.BrowserContextClockInstallParams = tObject({
  timeNumber: tOptional(tNumber),
  timeString: tOptional(tString),
});
scheme.BrowserContextClockInstallResult = tOptional(tObject({}));
scheme.BrowserContextClockPauseAtParams = tObject({
  timeNumber: tOptional(tNumber),
  timeString: tOptional(tString),
});
scheme.BrowserContextClockPauseAtResult = tOptional(tObject({}));
scheme.BrowserContextClockResumeParams = tOptional(tObject({}));
scheme.BrowserContextClockResumeResult = tOptional(tObject({}));
scheme.BrowserContextClockRunForParams = tObject({
  ticksNumber: tOptional(tNumber),
  ticksString: tOptional(tString),
});
scheme.BrowserContextClockRunForResult = tOptional(tObject({}));
scheme.BrowserContextClockSetFixedTimeParams = tObject({
  timeNumber: tOptional(tNumber),
  timeString: tOptional(tString),
});
scheme.BrowserContextClockSetFixedTimeResult = tOptional(tObject({}));
scheme.BrowserContextClockSetSystemTimeParams = tObject({
  timeNumber: tOptional(tNumber),
  timeString: tOptional(tString),
});
scheme.BrowserContextClockSetSystemTimeResult = tOptional(tObject({}));
scheme.PageInitializer = tObject({
  mainFrame: tChannel(['Frame']),
  viewportSize: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  isClosed: tBoolean,
  opener: tOptional(tChannel(['Page'])),
});
scheme.PageBindingCallEvent = tObject({
  binding: tChannel(['BindingCall']),
});
scheme.PageCloseEvent = tOptional(tObject({}));
scheme.PageCrashEvent = tOptional(tObject({}));
scheme.PageDownloadEvent = tObject({
  url: tString,
  suggestedFilename: tString,
  artifact: tChannel(['Artifact']),
});
scheme.PageViewportSizeChangedEvent = tObject({
  viewportSize: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
});
scheme.PageFileChooserEvent = tObject({
  element: tChannel(['ElementHandle']),
  isMultiple: tBoolean,
});
scheme.PageFrameAttachedEvent = tObject({
  frame: tChannel(['Frame']),
});
scheme.PageFrameDetachedEvent = tObject({
  frame: tChannel(['Frame']),
});
scheme.PageLocatorHandlerTriggeredEvent = tObject({
  uid: tNumber,
});
scheme.PageRouteEvent = tObject({
  route: tChannel(['Route']),
});
scheme.PageWebSocketRouteEvent = tObject({
  webSocketRoute: tChannel(['WebSocketRoute']),
});
scheme.PageVideoEvent = tObject({
  artifact: tChannel(['Artifact']),
});
scheme.PageWebSocketEvent = tObject({
  webSocket: tChannel(['WebSocket']),
});
scheme.PageWorkerEvent = tObject({
  worker: tChannel(['Worker']),
});
scheme.PageAddInitScriptParams = tObject({
  source: tString,
});
scheme.PageAddInitScriptResult = tOptional(tObject({}));
scheme.PageCloseParams = tObject({
  runBeforeUnload: tOptional(tBoolean),
  reason: tOptional(tString),
});
scheme.PageCloseResult = tOptional(tObject({}));
scheme.PageEmulateMediaParams = tObject({
  media: tOptional(tEnum(['screen', 'print', 'no-override'])),
  colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: tOptional(tEnum(['reduce', 'no-preference', 'no-override'])),
  forcedColors: tOptional(tEnum(['active', 'none', 'no-override'])),
  contrast: tOptional(tEnum(['no-preference', 'more', 'no-override'])),
});
scheme.PageEmulateMediaResult = tOptional(tObject({}));
scheme.PageExposeBindingParams = tObject({
  name: tString,
  needsHandle: tOptional(tBoolean),
});
scheme.PageExposeBindingResult = tOptional(tObject({}));
scheme.PageGoBackParams = tObject({
  timeout: tNumber,
  waitUntil: tOptional(tType('LifecycleEvent')),
});
scheme.PageGoBackResult = tObject({
  response: tOptional(tChannel(['Response'])),
});
scheme.PageGoForwardParams = tObject({
  timeout: tNumber,
  waitUntil: tOptional(tType('LifecycleEvent')),
});
scheme.PageGoForwardResult = tObject({
  response: tOptional(tChannel(['Response'])),
});
scheme.PageRequestGCParams = tOptional(tObject({}));
scheme.PageRequestGCResult = tOptional(tObject({}));
scheme.PageRegisterLocatorHandlerParams = tObject({
  selector: tString,
  noWaitAfter: tOptional(tBoolean),
});
scheme.PageRegisterLocatorHandlerResult = tObject({
  uid: tNumber,
});
scheme.PageResolveLocatorHandlerNoReplyParams = tObject({
  uid: tNumber,
  remove: tOptional(tBoolean),
});
scheme.PageResolveLocatorHandlerNoReplyResult = tOptional(tObject({}));
scheme.PageUnregisterLocatorHandlerParams = tObject({
  uid: tNumber,
});
scheme.PageUnregisterLocatorHandlerResult = tOptional(tObject({}));
scheme.PageReloadParams = tObject({
  timeout: tNumber,
  waitUntil: tOptional(tType('LifecycleEvent')),
});
scheme.PageReloadResult = tObject({
  response: tOptional(tChannel(['Response'])),
});
scheme.PageExpectScreenshotParams = tObject({
  expected: tOptional(tBinary),
  timeout: tNumber,
  isNot: tBoolean,
  locator: tOptional(tObject({
    frame: tChannel(['Frame']),
    selector: tString,
  })),
  comparator: tOptional(tString),
  maxDiffPixels: tOptional(tNumber),
  maxDiffPixelRatio: tOptional(tNumber),
  threshold: tOptional(tNumber),
  fullPage: tOptional(tBoolean),
  clip: tOptional(tType('Rect')),
  omitBackground: tOptional(tBoolean),
  caret: tOptional(tEnum(['hide', 'initial'])),
  animations: tOptional(tEnum(['disabled', 'allow'])),
  scale: tOptional(tEnum(['css', 'device'])),
  mask: tOptional(tArray(tObject({
    frame: tChannel(['Frame']),
    selector: tString,
  }))),
  maskColor: tOptional(tString),
  style: tOptional(tString),
});
scheme.PageExpectScreenshotResult = tObject({
  diff: tOptional(tBinary),
  errorMessage: tOptional(tString),
  actual: tOptional(tBinary),
  previous: tOptional(tBinary),
  timedOut: tOptional(tBoolean),
  log: tOptional(tArray(tString)),
});
scheme.PageScreenshotParams = tObject({
  timeout: tNumber,
  type: tOptional(tEnum(['png', 'jpeg'])),
  quality: tOptional(tNumber),
  fullPage: tOptional(tBoolean),
  clip: tOptional(tType('Rect')),
  omitBackground: tOptional(tBoolean),
  caret: tOptional(tEnum(['hide', 'initial'])),
  animations: tOptional(tEnum(['disabled', 'allow'])),
  scale: tOptional(tEnum(['css', 'device'])),
  mask: tOptional(tArray(tObject({
    frame: tChannel(['Frame']),
    selector: tString,
  }))),
  maskColor: tOptional(tString),
  style: tOptional(tString),
});
scheme.PageScreenshotResult = tObject({
  binary: tBinary,
});
scheme.PageSetExtraHTTPHeadersParams = tObject({
  headers: tArray(tType('NameValue')),
});
scheme.PageSetExtraHTTPHeadersResult = tOptional(tObject({}));
scheme.PageSetNetworkInterceptionPatternsParams = tObject({
  patterns: tArray(tObject({
    glob: tOptional(tString),
    regexSource: tOptional(tString),
    regexFlags: tOptional(tString),
  })),
});
scheme.PageSetNetworkInterceptionPatternsResult = tOptional(tObject({}));
scheme.PageSetWebSocketInterceptionPatternsParams = tObject({
  patterns: tArray(tObject({
    glob: tOptional(tString),
    regexSource: tOptional(tString),
    regexFlags: tOptional(tString),
  })),
});
scheme.PageSetWebSocketInterceptionPatternsResult = tOptional(tObject({}));
scheme.PageSetViewportSizeParams = tObject({
  viewportSize: tObject({
    width: tNumber,
    height: tNumber,
  }),
});
scheme.PageSetViewportSizeResult = tOptional(tObject({}));
scheme.PageKeyboardDownParams = tObject({
  key: tString,
});
scheme.PageKeyboardDownResult = tOptional(tObject({}));
scheme.PageKeyboardUpParams = tObject({
  key: tString,
});
scheme.PageKeyboardUpResult = tOptional(tObject({}));
scheme.PageKeyboardInsertTextParams = tObject({
  text: tString,
});
scheme.PageKeyboardInsertTextResult = tOptional(tObject({}));
scheme.PageKeyboardTypeParams = tObject({
  text: tString,
  delay: tOptional(tNumber),
});
scheme.PageKeyboardTypeResult = tOptional(tObject({}));
scheme.PageKeyboardPressParams = tObject({
  key: tString,
  delay: tOptional(tNumber),
});
scheme.PageKeyboardPressResult = tOptional(tObject({}));
scheme.PageMouseMoveParams = tObject({
  x: tNumber,
  y: tNumber,
  steps: tOptional(tNumber),
});
scheme.PageMouseMoveResult = tOptional(tObject({}));
scheme.PageMouseDownParams = tObject({
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  clickCount: tOptional(tNumber),
});
scheme.PageMouseDownResult = tOptional(tObject({}));
scheme.PageMouseUpParams = tObject({
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  clickCount: tOptional(tNumber),
});
scheme.PageMouseUpResult = tOptional(tObject({}));
scheme.PageMouseClickParams = tObject({
  x: tNumber,
  y: tNumber,
  delay: tOptional(tNumber),
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  clickCount: tOptional(tNumber),
});
scheme.PageMouseClickResult = tOptional(tObject({}));
scheme.PageMouseWheelParams = tObject({
  deltaX: tNumber,
  deltaY: tNumber,
});
scheme.PageMouseWheelResult = tOptional(tObject({}));
scheme.PageTouchscreenTapParams = tObject({
  x: tNumber,
  y: tNumber,
});
scheme.PageTouchscreenTapResult = tOptional(tObject({}));
scheme.PageAccessibilitySnapshotParams = tObject({
  interestingOnly: tOptional(tBoolean),
  root: tOptional(tChannel(['ElementHandle'])),
});
scheme.PageAccessibilitySnapshotResult = tObject({
  rootAXNode: tOptional(tType('AXNode')),
});
scheme.PagePdfParams = tObject({
  scale: tOptional(tNumber),
  displayHeaderFooter: tOptional(tBoolean),
  headerTemplate: tOptional(tString),
  footerTemplate: tOptional(tString),
  printBackground: tOptional(tBoolean),
  landscape: tOptional(tBoolean),
  pageRanges: tOptional(tString),
  format: tOptional(tString),
  width: tOptional(tString),
  height: tOptional(tString),
  preferCSSPageSize: tOptional(tBoolean),
  margin: tOptional(tObject({
    top: tOptional(tString),
    bottom: tOptional(tString),
    left: tOptional(tString),
    right: tOptional(tString),
  })),
  tagged: tOptional(tBoolean),
  outline: tOptional(tBoolean),
});
scheme.PagePdfResult = tObject({
  pdf: tBinary,
});
scheme.PageSnapshotForAIParams = tOptional(tObject({}));
scheme.PageSnapshotForAIResult = tObject({
  snapshot: tString,
});
scheme.PageStartJSCoverageParams = tObject({
  resetOnNavigation: tOptional(tBoolean),
  reportAnonymousScripts: tOptional(tBoolean),
});
scheme.PageStartJSCoverageResult = tOptional(tObject({}));
scheme.PageStopJSCoverageParams = tOptional(tObject({}));
scheme.PageStopJSCoverageResult = tObject({
  entries: tArray(tObject({
    url: tString,
    scriptId: tString,
    source: tOptional(tString),
    functions: tArray(tObject({
      functionName: tString,
      isBlockCoverage: tBoolean,
      ranges: tArray(tObject({
        startOffset: tNumber,
        endOffset: tNumber,
        count: tNumber,
      })),
    })),
  })),
});
scheme.PageStartCSSCoverageParams = tObject({
  resetOnNavigation: tOptional(tBoolean),
});
scheme.PageStartCSSCoverageResult = tOptional(tObject({}));
scheme.PageStopCSSCoverageParams = tOptional(tObject({}));
scheme.PageStopCSSCoverageResult = tObject({
  entries: tArray(tObject({
    url: tString,
    text: tOptional(tString),
    ranges: tArray(tObject({
      start: tNumber,
      end: tNumber,
    })),
  })),
});
scheme.PageBringToFrontParams = tOptional(tObject({}));
scheme.PageBringToFrontResult = tOptional(tObject({}));
scheme.PageUpdateSubscriptionParams = tObject({
  event: tEnum(['console', 'dialog', 'fileChooser', 'request', 'response', 'requestFinished', 'requestFailed']),
  enabled: tBoolean,
});
scheme.PageUpdateSubscriptionResult = tOptional(tObject({}));
scheme.FrameInitializer = tObject({
  url: tString,
  name: tString,
  parentFrame: tOptional(tChannel(['Frame'])),
  loadStates: tArray(tType('LifecycleEvent')),
});
scheme.FrameLoadstateEvent = tObject({
  add: tOptional(tType('LifecycleEvent')),
  remove: tOptional(tType('LifecycleEvent')),
});
scheme.FrameNavigatedEvent = tObject({
  url: tString,
  name: tString,
  newDocument: tOptional(tObject({
    request: tOptional(tChannel(['Request'])),
  })),
  error: tOptional(tString),
});
scheme.FrameEvalOnSelectorParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.FrameEvalOnSelectorResult = tObject({
  value: tType('SerializedValue'),
});
scheme.FrameEvalOnSelectorAllParams = tObject({
  selector: tString,
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.FrameEvalOnSelectorAllResult = tObject({
  value: tType('SerializedValue'),
});
scheme.FrameAddScriptTagParams = tObject({
  url: tOptional(tString),
  content: tOptional(tString),
  type: tOptional(tString),
});
scheme.FrameAddScriptTagResult = tObject({
  element: tChannel(['ElementHandle']),
});
scheme.FrameAddStyleTagParams = tObject({
  url: tOptional(tString),
  content: tOptional(tString),
});
scheme.FrameAddStyleTagResult = tObject({
  element: tChannel(['ElementHandle']),
});
scheme.FrameAriaSnapshotParams = tObject({
  selector: tString,
  forAI: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameAriaSnapshotResult = tObject({
  snapshot: tString,
});
scheme.FrameBlurParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameBlurResult = tOptional(tObject({}));
scheme.FrameCheckParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  force: tOptional(tBoolean),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.FrameCheckResult = tOptional(tObject({}));
scheme.FrameClickParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  force: tOptional(tBoolean),
  noWaitAfter: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  delay: tOptional(tNumber),
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  clickCount: tOptional(tNumber),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.FrameClickResult = tOptional(tObject({}));
scheme.FrameContentParams = tOptional(tObject({}));
scheme.FrameContentResult = tObject({
  value: tString,
});
scheme.FrameDragAndDropParams = tObject({
  source: tString,
  target: tString,
  force: tOptional(tBoolean),
  timeout: tNumber,
  trial: tOptional(tBoolean),
  sourcePosition: tOptional(tType('Point')),
  targetPosition: tOptional(tType('Point')),
  strict: tOptional(tBoolean),
});
scheme.FrameDragAndDropResult = tOptional(tObject({}));
scheme.FrameDblclickParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  force: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  delay: tOptional(tNumber),
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.FrameDblclickResult = tOptional(tObject({}));
scheme.FrameDispatchEventParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  type: tString,
  eventInit: tType('SerializedArgument'),
  timeout: tNumber,
});
scheme.FrameDispatchEventResult = tOptional(tObject({}));
scheme.FrameEvaluateExpressionParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.FrameEvaluateExpressionResult = tObject({
  value: tType('SerializedValue'),
});
scheme.FrameEvaluateExpressionHandleParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.FrameEvaluateExpressionHandleResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.FrameFillParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  value: tString,
  force: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameFillResult = tOptional(tObject({}));
scheme.FrameFocusParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameFocusResult = tOptional(tObject({}));
scheme.FrameFrameElementParams = tOptional(tObject({}));
scheme.FrameFrameElementResult = tObject({
  element: tChannel(['ElementHandle']),
});
scheme.FrameGenerateLocatorStringParams = tObject({
  selector: tString,
});
scheme.FrameGenerateLocatorStringResult = tObject({
  value: tOptional(tString),
});
scheme.FrameHighlightParams = tObject({
  selector: tString,
});
scheme.FrameHighlightResult = tOptional(tObject({}));
scheme.FrameGetAttributeParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  name: tString,
  timeout: tNumber,
});
scheme.FrameGetAttributeResult = tObject({
  value: tOptional(tString),
});
scheme.FrameGotoParams = tObject({
  url: tString,
  timeout: tNumber,
  waitUntil: tOptional(tType('LifecycleEvent')),
  referer: tOptional(tString),
});
scheme.FrameGotoResult = tObject({
  response: tOptional(tChannel(['Response'])),
});
scheme.FrameHoverParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  force: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.FrameHoverResult = tOptional(tObject({}));
scheme.FrameInnerHTMLParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameInnerHTMLResult = tObject({
  value: tString,
});
scheme.FrameInnerTextParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameInnerTextResult = tObject({
  value: tString,
});
scheme.FrameInputValueParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameInputValueResult = tObject({
  value: tString,
});
scheme.FrameIsCheckedParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameIsCheckedResult = tObject({
  value: tBoolean,
});
scheme.FrameIsDisabledParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameIsDisabledResult = tObject({
  value: tBoolean,
});
scheme.FrameIsEnabledParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameIsEnabledResult = tObject({
  value: tBoolean,
});
scheme.FrameIsHiddenParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
});
scheme.FrameIsHiddenResult = tObject({
  value: tBoolean,
});
scheme.FrameIsVisibleParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
});
scheme.FrameIsVisibleResult = tObject({
  value: tBoolean,
});
scheme.FrameIsEditableParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameIsEditableResult = tObject({
  value: tBoolean,
});
scheme.FramePressParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  key: tString,
  delay: tOptional(tNumber),
  noWaitAfter: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FramePressResult = tOptional(tObject({}));
scheme.FrameQuerySelectorParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
});
scheme.FrameQuerySelectorResult = tObject({
  element: tOptional(tChannel(['ElementHandle'])),
});
scheme.FrameQuerySelectorAllParams = tObject({
  selector: tString,
});
scheme.FrameQuerySelectorAllResult = tObject({
  elements: tArray(tChannel(['ElementHandle'])),
});
scheme.FrameQueryCountParams = tObject({
  selector: tString,
});
scheme.FrameQueryCountResult = tObject({
  value: tNumber,
});
scheme.FrameSelectOptionParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  elements: tOptional(tArray(tChannel(['ElementHandle']))),
  options: tOptional(tArray(tObject({
    valueOrLabel: tOptional(tString),
    value: tOptional(tString),
    label: tOptional(tString),
    index: tOptional(tNumber),
  }))),
  force: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameSelectOptionResult = tObject({
  values: tArray(tString),
});
scheme.FrameSetContentParams = tObject({
  html: tString,
  timeout: tNumber,
  waitUntil: tOptional(tType('LifecycleEvent')),
});
scheme.FrameSetContentResult = tOptional(tObject({}));
scheme.FrameSetInputFilesParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  payloads: tOptional(tArray(tObject({
    name: tString,
    mimeType: tOptional(tString),
    buffer: tBinary,
  }))),
  localDirectory: tOptional(tString),
  directoryStream: tOptional(tChannel(['WritableStream'])),
  localPaths: tOptional(tArray(tString)),
  streams: tOptional(tArray(tChannel(['WritableStream']))),
  timeout: tNumber,
});
scheme.FrameSetInputFilesResult = tOptional(tObject({}));
scheme.FrameTapParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  force: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.FrameTapResult = tOptional(tObject({}));
scheme.FrameTextContentParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.FrameTextContentResult = tObject({
  value: tOptional(tString),
});
scheme.FrameTitleParams = tOptional(tObject({}));
scheme.FrameTitleResult = tObject({
  value: tString,
});
scheme.FrameTypeParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  text: tString,
  delay: tOptional(tNumber),
  timeout: tNumber,
});
scheme.FrameTypeResult = tOptional(tObject({}));
scheme.FrameUncheckParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  force: tOptional(tBoolean),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.FrameUncheckResult = tOptional(tObject({}));
scheme.FrameWaitForTimeoutParams = tObject({
  waitTimeout: tNumber,
});
scheme.FrameWaitForTimeoutResult = tOptional(tObject({}));
scheme.FrameWaitForFunctionParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
  timeout: tNumber,
  pollingInterval: tOptional(tNumber),
});
scheme.FrameWaitForFunctionResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.FrameWaitForSelectorParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
  state: tOptional(tEnum(['attached', 'detached', 'visible', 'hidden'])),
  omitReturnValue: tOptional(tBoolean),
});
scheme.FrameWaitForSelectorResult = tObject({
  element: tOptional(tChannel(['ElementHandle'])),
});
scheme.FrameExpectParams = tObject({
  selector: tOptional(tString),
  expression: tString,
  expressionArg: tOptional(tAny),
  expectedText: tOptional(tArray(tType('ExpectedTextValue'))),
  expectedNumber: tOptional(tNumber),
  expectedValue: tOptional(tType('SerializedArgument')),
  useInnerText: tOptional(tBoolean),
  isNot: tBoolean,
  timeout: tNumber,
});
scheme.FrameExpectResult = tObject({
  matches: tBoolean,
  received: tOptional(tType('SerializedValue')),
  timedOut: tOptional(tBoolean),
  log: tOptional(tArray(tString)),
});
scheme.WorkerInitializer = tObject({
  url: tString,
});
scheme.WorkerCloseEvent = tOptional(tObject({}));
scheme.WorkerEvaluateExpressionParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.WorkerEvaluateExpressionResult = tObject({
  value: tType('SerializedValue'),
});
scheme.WorkerEvaluateExpressionHandleParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.WorkerEvaluateExpressionHandleResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.JSHandleInitializer = tObject({
  preview: tString,
});
scheme.JSHandlePreviewUpdatedEvent = tObject({
  preview: tString,
});
scheme.ElementHandlePreviewUpdatedEvent = tType('JSHandlePreviewUpdatedEvent');
scheme.JSHandleDisposeParams = tOptional(tObject({}));
scheme.ElementHandleDisposeParams = tType('JSHandleDisposeParams');
scheme.JSHandleDisposeResult = tOptional(tObject({}));
scheme.ElementHandleDisposeResult = tType('JSHandleDisposeResult');
scheme.JSHandleEvaluateExpressionParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.ElementHandleEvaluateExpressionParams = tType('JSHandleEvaluateExpressionParams');
scheme.JSHandleEvaluateExpressionResult = tObject({
  value: tType('SerializedValue'),
});
scheme.ElementHandleEvaluateExpressionResult = tType('JSHandleEvaluateExpressionResult');
scheme.JSHandleEvaluateExpressionHandleParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.ElementHandleEvaluateExpressionHandleParams = tType('JSHandleEvaluateExpressionHandleParams');
scheme.JSHandleEvaluateExpressionHandleResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.ElementHandleEvaluateExpressionHandleResult = tType('JSHandleEvaluateExpressionHandleResult');
scheme.JSHandleGetPropertyListParams = tOptional(tObject({}));
scheme.ElementHandleGetPropertyListParams = tType('JSHandleGetPropertyListParams');
scheme.JSHandleGetPropertyListResult = tObject({
  properties: tArray(tObject({
    name: tString,
    value: tChannel(['ElementHandle', 'JSHandle']),
  })),
});
scheme.ElementHandleGetPropertyListResult = tType('JSHandleGetPropertyListResult');
scheme.JSHandleGetPropertyParams = tObject({
  name: tString,
});
scheme.ElementHandleGetPropertyParams = tType('JSHandleGetPropertyParams');
scheme.JSHandleGetPropertyResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.ElementHandleGetPropertyResult = tType('JSHandleGetPropertyResult');
scheme.JSHandleJsonValueParams = tOptional(tObject({}));
scheme.ElementHandleJsonValueParams = tType('JSHandleJsonValueParams');
scheme.JSHandleJsonValueResult = tObject({
  value: tType('SerializedValue'),
});
scheme.ElementHandleJsonValueResult = tType('JSHandleJsonValueResult');
scheme.ElementHandleInitializer = tObject({
  preview: tString,
});
scheme.ElementHandleEvalOnSelectorParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.ElementHandleEvalOnSelectorResult = tObject({
  value: tType('SerializedValue'),
});
scheme.ElementHandleEvalOnSelectorAllParams = tObject({
  selector: tString,
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.ElementHandleEvalOnSelectorAllResult = tObject({
  value: tType('SerializedValue'),
});
scheme.ElementHandleBoundingBoxParams = tOptional(tObject({}));
scheme.ElementHandleBoundingBoxResult = tObject({
  value: tOptional(tType('Rect')),
});
scheme.ElementHandleCheckParams = tObject({
  force: tOptional(tBoolean),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.ElementHandleCheckResult = tOptional(tObject({}));
scheme.ElementHandleClickParams = tObject({
  force: tOptional(tBoolean),
  noWaitAfter: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  delay: tOptional(tNumber),
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  clickCount: tOptional(tNumber),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.ElementHandleClickResult = tOptional(tObject({}));
scheme.ElementHandleContentFrameParams = tOptional(tObject({}));
scheme.ElementHandleContentFrameResult = tObject({
  frame: tOptional(tChannel(['Frame'])),
});
scheme.ElementHandleDblclickParams = tObject({
  force: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  delay: tOptional(tNumber),
  button: tOptional(tEnum(['left', 'right', 'middle'])),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.ElementHandleDblclickResult = tOptional(tObject({}));
scheme.ElementHandleDispatchEventParams = tObject({
  type: tString,
  eventInit: tType('SerializedArgument'),
});
scheme.ElementHandleDispatchEventResult = tOptional(tObject({}));
scheme.ElementHandleFillParams = tObject({
  value: tString,
  force: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.ElementHandleFillResult = tOptional(tObject({}));
scheme.ElementHandleFocusParams = tOptional(tObject({}));
scheme.ElementHandleFocusResult = tOptional(tObject({}));
scheme.ElementHandleGetAttributeParams = tObject({
  name: tString,
});
scheme.ElementHandleGetAttributeResult = tObject({
  value: tOptional(tString),
});
scheme.ElementHandleHoverParams = tObject({
  force: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.ElementHandleHoverResult = tOptional(tObject({}));
scheme.ElementHandleInnerHTMLParams = tOptional(tObject({}));
scheme.ElementHandleInnerHTMLResult = tObject({
  value: tString,
});
scheme.ElementHandleInnerTextParams = tOptional(tObject({}));
scheme.ElementHandleInnerTextResult = tObject({
  value: tString,
});
scheme.ElementHandleInputValueParams = tOptional(tObject({}));
scheme.ElementHandleInputValueResult = tObject({
  value: tString,
});
scheme.ElementHandleIsCheckedParams = tOptional(tObject({}));
scheme.ElementHandleIsCheckedResult = tObject({
  value: tBoolean,
});
scheme.ElementHandleIsDisabledParams = tOptional(tObject({}));
scheme.ElementHandleIsDisabledResult = tObject({
  value: tBoolean,
});
scheme.ElementHandleIsEditableParams = tOptional(tObject({}));
scheme.ElementHandleIsEditableResult = tObject({
  value: tBoolean,
});
scheme.ElementHandleIsEnabledParams = tOptional(tObject({}));
scheme.ElementHandleIsEnabledResult = tObject({
  value: tBoolean,
});
scheme.ElementHandleIsHiddenParams = tOptional(tObject({}));
scheme.ElementHandleIsHiddenResult = tObject({
  value: tBoolean,
});
scheme.ElementHandleIsVisibleParams = tOptional(tObject({}));
scheme.ElementHandleIsVisibleResult = tObject({
  value: tBoolean,
});
scheme.ElementHandleOwnerFrameParams = tOptional(tObject({}));
scheme.ElementHandleOwnerFrameResult = tObject({
  frame: tOptional(tChannel(['Frame'])),
});
scheme.ElementHandlePressParams = tObject({
  key: tString,
  delay: tOptional(tNumber),
  timeout: tNumber,
  noWaitAfter: tOptional(tBoolean),
});
scheme.ElementHandlePressResult = tOptional(tObject({}));
scheme.ElementHandleQuerySelectorParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
});
scheme.ElementHandleQuerySelectorResult = tObject({
  element: tOptional(tChannel(['ElementHandle'])),
});
scheme.ElementHandleQuerySelectorAllParams = tObject({
  selector: tString,
});
scheme.ElementHandleQuerySelectorAllResult = tObject({
  elements: tArray(tChannel(['ElementHandle'])),
});
scheme.ElementHandleScreenshotParams = tObject({
  timeout: tNumber,
  type: tOptional(tEnum(['png', 'jpeg'])),
  quality: tOptional(tNumber),
  omitBackground: tOptional(tBoolean),
  caret: tOptional(tEnum(['hide', 'initial'])),
  animations: tOptional(tEnum(['disabled', 'allow'])),
  scale: tOptional(tEnum(['css', 'device'])),
  mask: tOptional(tArray(tObject({
    frame: tChannel(['Frame']),
    selector: tString,
  }))),
  maskColor: tOptional(tString),
  style: tOptional(tString),
});
scheme.ElementHandleScreenshotResult = tObject({
  binary: tBinary,
});
scheme.ElementHandleScrollIntoViewIfNeededParams = tObject({
  timeout: tNumber,
});
scheme.ElementHandleScrollIntoViewIfNeededResult = tOptional(tObject({}));
scheme.ElementHandleSelectOptionParams = tObject({
  elements: tOptional(tArray(tChannel(['ElementHandle']))),
  options: tOptional(tArray(tObject({
    valueOrLabel: tOptional(tString),
    value: tOptional(tString),
    label: tOptional(tString),
    index: tOptional(tNumber),
  }))),
  force: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.ElementHandleSelectOptionResult = tObject({
  values: tArray(tString),
});
scheme.ElementHandleSelectTextParams = tObject({
  force: tOptional(tBoolean),
  timeout: tNumber,
});
scheme.ElementHandleSelectTextResult = tOptional(tObject({}));
scheme.ElementHandleSetInputFilesParams = tObject({
  payloads: tOptional(tArray(tObject({
    name: tString,
    mimeType: tOptional(tString),
    buffer: tBinary,
  }))),
  localDirectory: tOptional(tString),
  directoryStream: tOptional(tChannel(['WritableStream'])),
  localPaths: tOptional(tArray(tString)),
  streams: tOptional(tArray(tChannel(['WritableStream']))),
  timeout: tNumber,
});
scheme.ElementHandleSetInputFilesResult = tOptional(tObject({}));
scheme.ElementHandleTapParams = tObject({
  force: tOptional(tBoolean),
  modifiers: tOptional(tArray(tEnum(['Alt', 'Control', 'ControlOrMeta', 'Meta', 'Shift']))),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.ElementHandleTapResult = tOptional(tObject({}));
scheme.ElementHandleTextContentParams = tOptional(tObject({}));
scheme.ElementHandleTextContentResult = tObject({
  value: tOptional(tString),
});
scheme.ElementHandleTypeParams = tObject({
  text: tString,
  delay: tOptional(tNumber),
  timeout: tNumber,
});
scheme.ElementHandleTypeResult = tOptional(tObject({}));
scheme.ElementHandleUncheckParams = tObject({
  force: tOptional(tBoolean),
  position: tOptional(tType('Point')),
  timeout: tNumber,
  trial: tOptional(tBoolean),
});
scheme.ElementHandleUncheckResult = tOptional(tObject({}));
scheme.ElementHandleWaitForElementStateParams = tObject({
  state: tEnum(['visible', 'hidden', 'stable', 'enabled', 'disabled', 'editable']),
  timeout: tNumber,
});
scheme.ElementHandleWaitForElementStateResult = tOptional(tObject({}));
scheme.ElementHandleWaitForSelectorParams = tObject({
  selector: tString,
  strict: tOptional(tBoolean),
  timeout: tNumber,
  state: tOptional(tEnum(['attached', 'detached', 'visible', 'hidden'])),
});
scheme.ElementHandleWaitForSelectorResult = tObject({
  element: tOptional(tChannel(['ElementHandle'])),
});
scheme.RequestInitializer = tObject({
  frame: tOptional(tChannel(['Frame'])),
  serviceWorker: tOptional(tChannel(['Worker'])),
  url: tString,
  resourceType: tString,
  method: tString,
  postData: tOptional(tBinary),
  headers: tArray(tType('NameValue')),
  isNavigationRequest: tBoolean,
  redirectedFrom: tOptional(tChannel(['Request'])),
});
scheme.RequestResponseParams = tOptional(tObject({}));
scheme.RequestResponseResult = tObject({
  response: tOptional(tChannel(['Response'])),
});
scheme.RequestRawRequestHeadersParams = tOptional(tObject({}));
scheme.RequestRawRequestHeadersResult = tObject({
  headers: tArray(tType('NameValue')),
});
scheme.RouteInitializer = tObject({
  request: tChannel(['Request']),
});
scheme.RouteRedirectNavigationRequestParams = tObject({
  url: tString,
});
scheme.RouteRedirectNavigationRequestResult = tOptional(tObject({}));
scheme.RouteAbortParams = tObject({
  errorCode: tOptional(tString),
});
scheme.RouteAbortResult = tOptional(tObject({}));
scheme.RouteContinueParams = tObject({
  url: tOptional(tString),
  method: tOptional(tString),
  headers: tOptional(tArray(tType('NameValue'))),
  postData: tOptional(tBinary),
  isFallback: tBoolean,
});
scheme.RouteContinueResult = tOptional(tObject({}));
scheme.RouteFulfillParams = tObject({
  status: tOptional(tNumber),
  headers: tOptional(tArray(tType('NameValue'))),
  body: tOptional(tString),
  isBase64: tOptional(tBoolean),
  fetchResponseUid: tOptional(tString),
});
scheme.RouteFulfillResult = tOptional(tObject({}));
scheme.WebSocketRouteInitializer = tObject({
  url: tString,
});
scheme.WebSocketRouteMessageFromPageEvent = tObject({
  message: tString,
  isBase64: tBoolean,
});
scheme.WebSocketRouteMessageFromServerEvent = tObject({
  message: tString,
  isBase64: tBoolean,
});
scheme.WebSocketRouteClosePageEvent = tObject({
  code: tOptional(tNumber),
  reason: tOptional(tString),
  wasClean: tBoolean,
});
scheme.WebSocketRouteCloseServerEvent = tObject({
  code: tOptional(tNumber),
  reason: tOptional(tString),
  wasClean: tBoolean,
});
scheme.WebSocketRouteConnectParams = tOptional(tObject({}));
scheme.WebSocketRouteConnectResult = tOptional(tObject({}));
scheme.WebSocketRouteEnsureOpenedParams = tOptional(tObject({}));
scheme.WebSocketRouteEnsureOpenedResult = tOptional(tObject({}));
scheme.WebSocketRouteSendToPageParams = tObject({
  message: tString,
  isBase64: tBoolean,
});
scheme.WebSocketRouteSendToPageResult = tOptional(tObject({}));
scheme.WebSocketRouteSendToServerParams = tObject({
  message: tString,
  isBase64: tBoolean,
});
scheme.WebSocketRouteSendToServerResult = tOptional(tObject({}));
scheme.WebSocketRouteClosePageParams = tObject({
  code: tOptional(tNumber),
  reason: tOptional(tString),
  wasClean: tBoolean,
});
scheme.WebSocketRouteClosePageResult = tOptional(tObject({}));
scheme.WebSocketRouteCloseServerParams = tObject({
  code: tOptional(tNumber),
  reason: tOptional(tString),
  wasClean: tBoolean,
});
scheme.WebSocketRouteCloseServerResult = tOptional(tObject({}));
scheme.ResourceTiming = tObject({
  startTime: tNumber,
  domainLookupStart: tNumber,
  domainLookupEnd: tNumber,
  connectStart: tNumber,
  secureConnectionStart: tNumber,
  connectEnd: tNumber,
  requestStart: tNumber,
  responseStart: tNumber,
});
scheme.ResponseInitializer = tObject({
  request: tChannel(['Request']),
  url: tString,
  status: tNumber,
  statusText: tString,
  headers: tArray(tType('NameValue')),
  timing: tType('ResourceTiming'),
  fromServiceWorker: tBoolean,
});
scheme.ResponseBodyParams = tOptional(tObject({}));
scheme.ResponseBodyResult = tObject({
  binary: tBinary,
});
scheme.ResponseSecurityDetailsParams = tOptional(tObject({}));
scheme.ResponseSecurityDetailsResult = tObject({
  value: tOptional(tType('SecurityDetails')),
});
scheme.ResponseServerAddrParams = tOptional(tObject({}));
scheme.ResponseServerAddrResult = tObject({
  value: tOptional(tType('RemoteAddr')),
});
scheme.ResponseRawResponseHeadersParams = tOptional(tObject({}));
scheme.ResponseRawResponseHeadersResult = tObject({
  headers: tArray(tType('NameValue')),
});
scheme.ResponseSizesParams = tOptional(tObject({}));
scheme.ResponseSizesResult = tObject({
  sizes: tType('RequestSizes'),
});
scheme.SecurityDetails = tObject({
  issuer: tOptional(tString),
  protocol: tOptional(tString),
  subjectName: tOptional(tString),
  validFrom: tOptional(tNumber),
  validTo: tOptional(tNumber),
});
scheme.RequestSizes = tObject({
  requestBodySize: tNumber,
  requestHeadersSize: tNumber,
  responseBodySize: tNumber,
  responseHeadersSize: tNumber,
});
scheme.RemoteAddr = tObject({
  ipAddress: tString,
  port: tNumber,
});
scheme.WebSocketInitializer = tObject({
  url: tString,
});
scheme.WebSocketOpenEvent = tOptional(tObject({}));
scheme.WebSocketFrameSentEvent = tObject({
  opcode: tNumber,
  data: tString,
});
scheme.WebSocketFrameReceivedEvent = tObject({
  opcode: tNumber,
  data: tString,
});
scheme.WebSocketSocketErrorEvent = tObject({
  error: tString,
});
scheme.WebSocketCloseEvent = tOptional(tObject({}));
scheme.BindingCallInitializer = tObject({
  frame: tChannel(['Frame']),
  name: tString,
  args: tOptional(tArray(tType('SerializedValue'))),
  handle: tOptional(tChannel(['ElementHandle', 'JSHandle'])),
});
scheme.BindingCallRejectParams = tObject({
  error: tType('SerializedError'),
});
scheme.BindingCallRejectResult = tOptional(tObject({}));
scheme.BindingCallResolveParams = tObject({
  result: tType('SerializedArgument'),
});
scheme.BindingCallResolveResult = tOptional(tObject({}));
scheme.DialogInitializer = tObject({
  page: tOptional(tChannel(['Page'])),
  type: tString,
  message: tString,
  defaultValue: tString,
});
scheme.DialogAcceptParams = tObject({
  promptText: tOptional(tString),
});
scheme.DialogAcceptResult = tOptional(tObject({}));
scheme.DialogDismissParams = tOptional(tObject({}));
scheme.DialogDismissResult = tOptional(tObject({}));
scheme.TracingInitializer = tOptional(tObject({}));
scheme.TracingTracingStartParams = tObject({
  name: tOptional(tString),
  snapshots: tOptional(tBoolean),
  screenshots: tOptional(tBoolean),
  live: tOptional(tBoolean),
});
scheme.TracingTracingStartResult = tOptional(tObject({}));
scheme.TracingTracingStartChunkParams = tObject({
  name: tOptional(tString),
  title: tOptional(tString),
});
scheme.TracingTracingStartChunkResult = tObject({
  traceName: tString,
});
scheme.TracingTracingGroupParams = tObject({
  name: tString,
  location: tOptional(tObject({
    file: tString,
    line: tOptional(tNumber),
    column: tOptional(tNumber),
  })),
});
scheme.TracingTracingGroupResult = tOptional(tObject({}));
scheme.TracingTracingGroupEndParams = tOptional(tObject({}));
scheme.TracingTracingGroupEndResult = tOptional(tObject({}));
scheme.TracingTracingStopChunkParams = tObject({
  mode: tEnum(['archive', 'discard', 'entries']),
});
scheme.TracingTracingStopChunkResult = tObject({
  artifact: tOptional(tChannel(['Artifact'])),
  entries: tOptional(tArray(tType('NameValue'))),
});
scheme.TracingTracingStopParams = tOptional(tObject({}));
scheme.TracingTracingStopResult = tOptional(tObject({}));
scheme.ArtifactInitializer = tObject({
  absolutePath: tString,
});
scheme.ArtifactPathAfterFinishedParams = tOptional(tObject({}));
scheme.ArtifactPathAfterFinishedResult = tObject({
  value: tString,
});
scheme.ArtifactSaveAsParams = tObject({
  path: tString,
});
scheme.ArtifactSaveAsResult = tOptional(tObject({}));
scheme.ArtifactSaveAsStreamParams = tOptional(tObject({}));
scheme.ArtifactSaveAsStreamResult = tObject({
  stream: tChannel(['Stream']),
});
scheme.ArtifactFailureParams = tOptional(tObject({}));
scheme.ArtifactFailureResult = tObject({
  error: tOptional(tString),
});
scheme.ArtifactStreamParams = tOptional(tObject({}));
scheme.ArtifactStreamResult = tObject({
  stream: tChannel(['Stream']),
});
scheme.ArtifactCancelParams = tOptional(tObject({}));
scheme.ArtifactCancelResult = tOptional(tObject({}));
scheme.ArtifactDeleteParams = tOptional(tObject({}));
scheme.ArtifactDeleteResult = tOptional(tObject({}));
scheme.StreamInitializer = tOptional(tObject({}));
scheme.StreamReadParams = tObject({
  size: tOptional(tNumber),
});
scheme.StreamReadResult = tObject({
  binary: tBinary,
});
scheme.StreamCloseParams = tOptional(tObject({}));
scheme.StreamCloseResult = tOptional(tObject({}));
scheme.WritableStreamInitializer = tOptional(tObject({}));
scheme.WritableStreamWriteParams = tObject({
  binary: tBinary,
});
scheme.WritableStreamWriteResult = tOptional(tObject({}));
scheme.WritableStreamCloseParams = tOptional(tObject({}));
scheme.WritableStreamCloseResult = tOptional(tObject({}));
scheme.CDPSessionInitializer = tOptional(tObject({}));
scheme.CDPSessionEventEvent = tObject({
  method: tString,
  params: tOptional(tAny),
});
scheme.CDPSessionSendParams = tObject({
  method: tString,
  params: tOptional(tAny),
});
scheme.CDPSessionSendResult = tObject({
  result: tAny,
});
scheme.CDPSessionDetachParams = tOptional(tObject({}));
scheme.CDPSessionDetachResult = tOptional(tObject({}));
scheme.ElectronInitializer = tOptional(tObject({}));
scheme.ElectronLaunchParams = tObject({
  executablePath: tOptional(tString),
  args: tOptional(tArray(tString)),
  cwd: tOptional(tString),
  env: tOptional(tArray(tType('NameValue'))),
  timeout: tNumber,
  acceptDownloads: tOptional(tEnum(['accept', 'deny', 'internal-browser-default'])),
  bypassCSP: tOptional(tBoolean),
  colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
  extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
  geolocation: tOptional(tObject({
    longitude: tNumber,
    latitude: tNumber,
    accuracy: tOptional(tNumber),
  })),
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
  })),
  ignoreHTTPSErrors: tOptional(tBoolean),
  locale: tOptional(tString),
  offline: tOptional(tBoolean),
  recordVideo: tOptional(tObject({
    dir: tString,
    size: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
  })),
  strictSelectors: tOptional(tBoolean),
  timezoneId: tOptional(tString),
  tracesDir: tOptional(tString),
  selectorEngines: tOptional(tArray(tType('SelectorEngine'))),
  testIdAttributeName: tOptional(tString),
});
scheme.ElectronLaunchResult = tObject({
  electronApplication: tChannel(['ElectronApplication']),
});
scheme.ElectronApplicationInitializer = tObject({
  context: tChannel(['BrowserContext']),
});
scheme.ElectronApplicationCloseEvent = tOptional(tObject({}));
scheme.ElectronApplicationConsoleEvent = tObject({
  type: tString,
  text: tString,
  args: tArray(tChannel(['ElementHandle', 'JSHandle'])),
  location: tObject({
    url: tString,
    lineNumber: tNumber,
    columnNumber: tNumber,
  }),
});
scheme.ElectronApplicationBrowserWindowParams = tObject({
  page: tChannel(['Page']),
});
scheme.ElectronApplicationBrowserWindowResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.ElectronApplicationEvaluateExpressionParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.ElectronApplicationEvaluateExpressionResult = tObject({
  value: tType('SerializedValue'),
});
scheme.ElectronApplicationEvaluateExpressionHandleParams = tObject({
  expression: tString,
  isFunction: tOptional(tBoolean),
  arg: tType('SerializedArgument'),
});
scheme.ElectronApplicationEvaluateExpressionHandleResult = tObject({
  handle: tChannel(['ElementHandle', 'JSHandle']),
});
scheme.ElectronApplicationUpdateSubscriptionParams = tObject({
  event: tEnum(['console']),
  enabled: tBoolean,
});
scheme.ElectronApplicationUpdateSubscriptionResult = tOptional(tObject({}));
scheme.AndroidInitializer = tOptional(tObject({}));
scheme.AndroidDevicesParams = tObject({
  host: tOptional(tString),
  port: tOptional(tNumber),
  omitDriverInstall: tOptional(tBoolean),
});
scheme.AndroidDevicesResult = tObject({
  devices: tArray(tChannel(['AndroidDevice'])),
});
scheme.AndroidSocketInitializer = tOptional(tObject({}));
scheme.AndroidSocketDataEvent = tObject({
  data: tBinary,
});
scheme.AndroidSocketCloseEvent = tOptional(tObject({}));
scheme.AndroidSocketWriteParams = tObject({
  data: tBinary,
});
scheme.AndroidSocketWriteResult = tOptional(tObject({}));
scheme.AndroidSocketCloseParams = tOptional(tObject({}));
scheme.AndroidSocketCloseResult = tOptional(tObject({}));
scheme.AndroidDeviceInitializer = tObject({
  model: tString,
  serial: tString,
});
scheme.AndroidDeviceCloseEvent = tOptional(tObject({}));
scheme.AndroidDeviceWebViewAddedEvent = tObject({
  webView: tType('AndroidWebView'),
});
scheme.AndroidDeviceWebViewRemovedEvent = tObject({
  socketName: tString,
});
scheme.AndroidDeviceWaitParams = tObject({
  androidSelector: tType('AndroidSelector'),
  state: tOptional(tEnum(['gone'])),
  timeout: tNumber,
});
scheme.AndroidDeviceWaitResult = tOptional(tObject({}));
scheme.AndroidDeviceFillParams = tObject({
  androidSelector: tType('AndroidSelector'),
  text: tString,
  timeout: tNumber,
});
scheme.AndroidDeviceFillResult = tOptional(tObject({}));
scheme.AndroidDeviceTapParams = tObject({
  androidSelector: tType('AndroidSelector'),
  duration: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDeviceTapResult = tOptional(tObject({}));
scheme.AndroidDeviceDragParams = tObject({
  androidSelector: tType('AndroidSelector'),
  dest: tType('Point'),
  speed: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDeviceDragResult = tOptional(tObject({}));
scheme.AndroidDeviceFlingParams = tObject({
  androidSelector: tType('AndroidSelector'),
  direction: tEnum(['up', 'down', 'left', 'right']),
  speed: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDeviceFlingResult = tOptional(tObject({}));
scheme.AndroidDeviceLongTapParams = tObject({
  androidSelector: tType('AndroidSelector'),
  timeout: tNumber,
});
scheme.AndroidDeviceLongTapResult = tOptional(tObject({}));
scheme.AndroidDevicePinchCloseParams = tObject({
  androidSelector: tType('AndroidSelector'),
  percent: tNumber,
  speed: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDevicePinchCloseResult = tOptional(tObject({}));
scheme.AndroidDevicePinchOpenParams = tObject({
  androidSelector: tType('AndroidSelector'),
  percent: tNumber,
  speed: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDevicePinchOpenResult = tOptional(tObject({}));
scheme.AndroidDeviceScrollParams = tObject({
  androidSelector: tType('AndroidSelector'),
  direction: tEnum(['up', 'down', 'left', 'right']),
  percent: tNumber,
  speed: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDeviceScrollResult = tOptional(tObject({}));
scheme.AndroidDeviceSwipeParams = tObject({
  androidSelector: tType('AndroidSelector'),
  direction: tEnum(['up', 'down', 'left', 'right']),
  percent: tNumber,
  speed: tOptional(tNumber),
  timeout: tNumber,
});
scheme.AndroidDeviceSwipeResult = tOptional(tObject({}));
scheme.AndroidDeviceInfoParams = tObject({
  androidSelector: tType('AndroidSelector'),
});
scheme.AndroidDeviceInfoResult = tObject({
  info: tType('AndroidElementInfo'),
});
scheme.AndroidDeviceScreenshotParams = tOptional(tObject({}));
scheme.AndroidDeviceScreenshotResult = tObject({
  binary: tBinary,
});
scheme.AndroidDeviceInputTypeParams = tObject({
  text: tString,
});
scheme.AndroidDeviceInputTypeResult = tOptional(tObject({}));
scheme.AndroidDeviceInputPressParams = tObject({
  key: tString,
});
scheme.AndroidDeviceInputPressResult = tOptional(tObject({}));
scheme.AndroidDeviceInputTapParams = tObject({
  point: tType('Point'),
});
scheme.AndroidDeviceInputTapResult = tOptional(tObject({}));
scheme.AndroidDeviceInputSwipeParams = tObject({
  segments: tArray(tType('Point')),
  steps: tNumber,
});
scheme.AndroidDeviceInputSwipeResult = tOptional(tObject({}));
scheme.AndroidDeviceInputDragParams = tObject({
  from: tType('Point'),
  to: tType('Point'),
  steps: tNumber,
});
scheme.AndroidDeviceInputDragResult = tOptional(tObject({}));
scheme.AndroidDeviceLaunchBrowserParams = tObject({
  noDefaultViewport: tOptional(tBoolean),
  viewport: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  screen: tOptional(tObject({
    width: tNumber,
    height: tNumber,
  })),
  ignoreHTTPSErrors: tOptional(tBoolean),
  clientCertificates: tOptional(tArray(tObject({
    origin: tString,
    cert: tOptional(tBinary),
    key: tOptional(tBinary),
    passphrase: tOptional(tString),
    pfx: tOptional(tBinary),
  }))),
  javaScriptEnabled: tOptional(tBoolean),
  bypassCSP: tOptional(tBoolean),
  userAgent: tOptional(tString),
  locale: tOptional(tString),
  timezoneId: tOptional(tString),
  geolocation: tOptional(tObject({
    longitude: tNumber,
    latitude: tNumber,
    accuracy: tOptional(tNumber),
  })),
  permissions: tOptional(tArray(tString)),
  extraHTTPHeaders: tOptional(tArray(tType('NameValue'))),
  offline: tOptional(tBoolean),
  httpCredentials: tOptional(tObject({
    username: tString,
    password: tString,
    origin: tOptional(tString),
    send: tOptional(tEnum(['always', 'unauthorized'])),
  })),
  deviceScaleFactor: tOptional(tNumber),
  isMobile: tOptional(tBoolean),
  hasTouch: tOptional(tBoolean),
  colorScheme: tOptional(tEnum(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: tOptional(tEnum(['reduce', 'no-preference', 'no-override'])),
  forcedColors: tOptional(tEnum(['active', 'none', 'no-override'])),
  acceptDownloads: tOptional(tEnum(['accept', 'deny', 'internal-browser-default'])),
  contrast: tOptional(tEnum(['no-preference', 'more', 'no-override'])),
  baseURL: tOptional(tString),
  recordVideo: tOptional(tObject({
    dir: tString,
    size: tOptional(tObject({
      width: tNumber,
      height: tNumber,
    })),
  })),
  strictSelectors: tOptional(tBoolean),
  serviceWorkers: tOptional(tEnum(['allow', 'block'])),
  selectorEngines: tOptional(tArray(tType('SelectorEngine'))),
  testIdAttributeName: tOptional(tString),
  pkg: tOptional(tString),
  args: tOptional(tArray(tString)),
  proxy: tOptional(tObject({
    server: tString,
    bypass: tOptional(tString),
    username: tOptional(tString),
    password: tOptional(tString),
  })),
});
scheme.AndroidDeviceLaunchBrowserResult = tObject({
  context: tChannel(['BrowserContext']),
});
scheme.AndroidDeviceOpenParams = tObject({
  command: tString,
});
scheme.AndroidDeviceOpenResult = tObject({
  socket: tChannel(['AndroidSocket']),
});
scheme.AndroidDeviceShellParams = tObject({
  command: tString,
});
scheme.AndroidDeviceShellResult = tObject({
  result: tBinary,
});
scheme.AndroidDeviceInstallApkParams = tObject({
  file: tBinary,
  args: tOptional(tArray(tString)),
});
scheme.AndroidDeviceInstallApkResult = tOptional(tObject({}));
scheme.AndroidDevicePushParams = tObject({
  file: tBinary,
  path: tString,
  mode: tOptional(tNumber),
});
scheme.AndroidDevicePushResult = tOptional(tObject({}));
scheme.AndroidDeviceConnectToWebViewParams = tObject({
  socketName: tString,
});
scheme.AndroidDeviceConnectToWebViewResult = tObject({
  context: tChannel(['BrowserContext']),
});
scheme.AndroidDeviceCloseParams = tOptional(tObject({}));
scheme.AndroidDeviceCloseResult = tOptional(tObject({}));
scheme.AndroidWebView = tObject({
  pid: tNumber,
  pkg: tString,
  socketName: tString,
});
scheme.AndroidSelector = tObject({
  checkable: tOptional(tBoolean),
  checked: tOptional(tBoolean),
  clazz: tOptional(tString),
  clickable: tOptional(tBoolean),
  depth: tOptional(tNumber),
  desc: tOptional(tString),
  enabled: tOptional(tBoolean),
  focusable: tOptional(tBoolean),
  focused: tOptional(tBoolean),
  hasChild: tOptional(tObject({
    androidSelector: tType('AndroidSelector'),
  })),
  hasDescendant: tOptional(tObject({
    androidSelector: tType('AndroidSelector'),
    maxDepth: tOptional(tNumber),
  })),
  longClickable: tOptional(tBoolean),
  pkg: tOptional(tString),
  res: tOptional(tString),
  scrollable: tOptional(tBoolean),
  selected: tOptional(tBoolean),
  text: tOptional(tString),
});
scheme.AndroidElementInfo = tObject({
  children: tOptional(tArray(tType('AndroidElementInfo'))),
  clazz: tString,
  desc: tString,
  res: tString,
  pkg: tString,
  text: tString,
  bounds: tType('Rect'),
  checkable: tBoolean,
  checked: tBoolean,
  clickable: tBoolean,
  enabled: tBoolean,
  focusable: tBoolean,
  focused: tBoolean,
  longClickable: tBoolean,
  scrollable: tBoolean,
  selected: tBoolean,
});
scheme.JsonPipeInitializer = tOptional(tObject({}));
scheme.JsonPipeMessageEvent = tObject({
  message: tAny,
});
scheme.JsonPipeClosedEvent = tObject({
  reason: tOptional(tString),
});
scheme.JsonPipeSendParams = tObject({
  message: tAny,
});
scheme.JsonPipeSendResult = tOptional(tObject({}));
scheme.JsonPipeCloseParams = tOptional(tObject({}));
scheme.JsonPipeCloseResult = tOptional(tObject({}));
