/**
 * 🚀 修复的无Patch解决方案 - 绕过exports限制
 * 使用直接文件路径访问Playwright内部API
 */

const { chromium } = require('playwright');
const path = require('path');

// 🔧 尝试直接从文件路径导入API（绕过exports限制）
function tryImportPlaywrightAPI() {
  try {
    // 找到playwright-core的安装路径
    const playwrightCorePath = require.resolve('playwright-core');
    const playwrightCoreDir = path.dirname(playwrightCorePath);
    
    console.log('📍 Playwright Core 路径:', playwrightCoreDir);
    
    // 尝试直接路径导入
    const generateCode = require(path.join(playwrightCoreDir, 'lib/server/codegen/language.js')).generateCode;
    const languageSet = require(path.join(playwrightCoreDir, 'lib/server/codegen/languages.js')).languageSet;
    const collapseActions = require(path.join(playwrightCoreDir, 'lib/server/recorder/recorderUtils.js')).collapseActions;
    
    console.log('✅ 成功导入Playwright内部API');
    return { generateCode, languageSet, collapseActions };
    
  } catch (error) {
    console.log('❌ 无法导入Playwright内部API:', error.message);
    return null;
  }
}

// 🎯 备用方案：复制官方实现的核心逻辑
function createFallbackImplementation() {
  console.log('🔄 使用备用实现...');
  
  // 简化的语言生成器
  const fallbackLanguageSet = () => [
    {
      id: 'javascript',
      name: 'JavaScript',
      groupName: 'Node.js',
      highlighter: 'javascript',
      generateHeader: (options) => {
        return `const { ${options.browserName || 'chromium'} } = require('playwright');\n\n(async () => {\n  const browser = await ${options.browserName || 'chromium'}.launch(${JSON.stringify(options.launchOptions || { headless: false })});\n  const context = await browser.newContext(${JSON.stringify(options.contextOptions || {})});\n  const page = await context.newPage();`;
      },
      generateFooter: () => {
        return '\n\n  await context.close();\n  await browser.close();\n})();';
      },
      generateAction: (action) => {
        switch (action.name) {
          case 'navigate':
            return `  await page.goto('${action.url}');`;
          case 'click':
            return `  await page.click('${action.selector}');`;
          case 'fill':
            return `  await page.fill('${action.selector}', '${action.text}');`;
          case 'press':
            return `  await page.press('${action.selector}', '${action.key}');`;
          case 'check':
            return `  await page.check('${action.selector}');`;
          case 'uncheck':
            return `  await page.uncheck('${action.selector}');`;
          case 'select':
            return `  await page.selectOption('${action.selector}', ${JSON.stringify(action.options)});`;
          default:
            return `  // ${action.name}: ${JSON.stringify(action)}`;
        }
      }
    },
    {
      id: 'jsonl',
      name: 'JSON Lines',
      groupName: 'Data',
      highlighter: 'json',
      generateHeader: () => '',
      generateFooter: () => '',
      generateAction: (action) => JSON.stringify(action)
    }
  ];
  
  // 简化的动作合并
  const fallbackCollapseActions = (actions) => {
    // 基本实现：去除重复的导航动作
    const collapsed = [];
    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];
      const prev = collapsed[collapsed.length - 1];
      
      // 如果是连续的导航到同一页面，只保留最后一个
      if (prev && prev.action.name === 'navigate' && action.action.name === 'navigate' && prev.action.url === action.action.url) {
        continue;
      }
      
      collapsed.push(action);
    }
    return collapsed;
  };
  
  // 简化的代码生成
  const fallbackGenerateCode = (actions, languageGenerator, options) => {
    const header = languageGenerator.generateHeader(options);
    const footer = languageGenerator.generateFooter();
    
    const actionTexts = actions.map(actionInContext => {
      return languageGenerator.generateAction(actionInContext.action);
    });
    
    const text = [header, ...actionTexts, footer].join('\n');
    
    return {
      header,
      footer,
      actionTexts,
      text
    };
  };
  
  return {
    generateCode: fallbackGenerateCode,
    languageSet: fallbackLanguageSet,
    collapseActions: fallbackCollapseActions
  };
}

class PlaywrightCodeGeneratorFixed {
  constructor(options = {}) {
    this.actions = [];
    this.options = {
      browserName: 'chromium',
      launchOptions: { headless: false },
      contextOptions: {},
      deviceName: undefined,
      saveStorage: undefined,
      ...options
    };
    
    // 尝试导入官方API，失败则使用备用实现
    const apis = tryImportPlaywrightAPI() || createFallbackImplementation();
    this.generateCode = apis.generateCode;
    this.languageSet = apis.languageSet;
    this.collapseActions = apis.collapseActions;
    
    // 设置环境变量隐藏工具栏
    process.env.PW_CODEGEN_NO_INSPECTOR = '1';
  }

  /**
   * 🎯 生成所有语言的代码
   */
  generateAllSources() {
    if (this.actions.length === 0) return [];

    try {
      const timestamp = Date.now();
      const sources = [];
      const collapsedActions = this.collapseActions(this.actions);
      
      console.log(`📊 生成代码 - 原始动作: ${this.actions.length}, 合并后: ${collapsedActions.length}`);

      // 生成所有语言的代码
      for (const languageGenerator of this.languageSet()) {
        const { header, footer, actionTexts, text } = this.generateCode(
          collapsedActions, 
          languageGenerator, 
          this.options
        );
        
        const source = {
          isPrimary: languageGenerator.id === 'javascript',
          timestamp,
          isRecorded: true,
          label: languageGenerator.name,
          group: languageGenerator.groupName,
          id: languageGenerator.id,
          text,
          header,
          footer,
          actions: actionTexts,
          language: languageGenerator.highlighter,
          highlight: []
        };
        
        source.revealLine = text.split('\n').length - 1;
        sources.push(source);
      }
      
      return sources;
    } catch (error) {
      console.error('🚨 代码生成失败:', error);
      return [];
    }
  }

  /**
   * 🎯 处理ActionAdded事件
   */
  onActionAdded(page, actionInContext) {
    console.log('➕ ActionAdded:', actionInContext.action.name);
    this.actions.push(actionInContext);
    this.updateAndNotify('actionAdded');
  }

  /**
   * 🎯 处理ActionUpdated事件
   */
  onActionUpdated(page, actionInContext) {
    console.log('🔄 ActionUpdated:', actionInContext.action.name);
    // 找到并替换最后一个相同的动作
    const lastIndex = this.actions.findLastIndex(
      a => a.frame.pageGuid === actionInContext.frame.pageGuid &&
           a.action.name === actionInContext.action.name
    );
    
    if (lastIndex !== -1) {
      this.actions[lastIndex] = actionInContext;
      this.updateAndNotify('actionUpdated');
    } else {
      // 如果没找到，当作新动作处理
      this.onActionAdded(page, actionInContext);
    }
  }

  /**
   * 🎯 处理SignalAdded事件
   */
  onSignalAdded(page, signal) {
    console.log('📡 SignalAdded:', signal.signal.name);
    
    // 将信号添加到最后一个匹配的动作中
    const lastAction = this.actions.findLast(
      a => a.frame.pageGuid === signal.frame.pageGuid
    );
    
    if (lastAction) {
      if (!lastAction.action.signals) {
        lastAction.action.signals = [];
      }
      lastAction.action.signals.push(signal.signal);
      console.log(`🔗 信号 ${signal.signal.name} 已添加到动作 ${lastAction.action.name}`);
      this.updateAndNotify('signalAdded');
    }
  }

  /**
   * 🔄 更新代码并通知
   */
  updateAndNotify(eventType) {
    const sources = this.generateAllSources();
    
    if (sources.length > 0) {
      this.notifyCodeGenerated(sources, eventType);
    }
  }

  /**
   * 📡 通知代码生成完成
   */
  notifyCodeGenerated(sources, eventType) {
    const jsSource = sources.find(s => s.id === 'javascript');
    const jsonSource = sources.find(s => s.id === 'jsonl');
    
    console.log(`\n🎯 代码生成完成 (触发: ${eventType})`);
    console.log(`📄 生成了 ${sources.length} 种语言的代码`);
    
    if (jsSource) {
      console.log('\n🟢 JavaScript 代码:');
      console.log('─'.repeat(50));
      console.log(jsSource.text);
      console.log('─'.repeat(50));
    }
    
    if (jsonSource) {
      console.log('\n🟡 JSONL 代码:');
      console.log('─'.repeat(50));
      console.log(jsonSource.text);
      console.log('─'.repeat(50));
    }

    // 发送到你的Electron应用
    if (global.electronPlaywrightRecorder) {
      global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated({
        type: 'playwrightCodeGenerated',
        sources: sources,
        timestamp: Date.now(),
        isApiMode: true,
        actionCount: this.actions.length,
        eventType: eventType
      });
    }
  }

  /**
   * 🧹 清空所有动作
   */
  clear() {
    console.log('🧹 清空所有动作');
    this.actions = [];
    this.updateAndNotify('clear');
  }
}

/**
 * 🚀 启动修复版录制器
 */
async function startFixedRecorder() {
  console.log('🧪 启动修复版无Patch解决方案测试...');
  
  // 模拟Electron环境
  global.electronPlaywrightRecorder = {
    messageHandler: {
      _handlePlaywrightCodeGenerated(data) {
        console.log('\n📡 收到代码生成数据:');
        console.log(`✅ 触发事件: ${data.eventType}`);
        console.log(`📊 动作数量: ${data.actionCount}`);
        console.log(`📄 代码语言: ${data.sources.length}`);
      }
    }
  };

  const codeGenerator = new PlaywrightCodeGeneratorFixed({
    browserName: 'chromium',
    launchOptions: { headless: false },
    contextOptions: { viewport: null }
  });

  const browser = await chromium.launch({ 
    headless: false,
    args: ['--start-maximized']
  });

  try {
    const context = await browser.newContext();
    
    // 使用官方API，监听所有必要事件
    await context._enableRecorder({
      language: 'javascript',
      mode: 'recording',
      recorderMode: 'api',  // API模式，隐藏工具栏
    }, {
      // 监听所有事件
      actionAdded: (page, actionInContext) => {
        codeGenerator.onActionAdded(page, actionInContext);
      },
      
      actionUpdated: (page, actionInContext) => {
        codeGenerator.onActionUpdated(page, actionInContext);
      },
      
      signalAdded: (page, signal) => {
        codeGenerator.onSignalAdded(page, signal);
      }
    });

    const page = await context.newPage();
    
    console.log('\n🎬 录制开始，请执行一些操作...');
    console.log('ℹ️ 工具栏已隐藏，代码将通过回调生成');
    
    // 导航到测试页面
    await page.goto('https://example.com');
    await page.waitForTimeout(1000);
    
    // 执行一些测试操作
    try {
      await page.click('a', { timeout: 2000 });
      console.log('🖱️ 执行了点击操作');
    } catch {
      console.log('ℹ️ 无可点击元素');
    }

    console.log('\n⏳ 等待更多操作...');
    await page.waitForTimeout(3000);
    
    console.log('\n✅ 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
if (require.main === module) {
  startFixedRecorder().catch(console.error);
}

module.exports = { PlaywrightCodeGeneratorFixed, startFixedRecorder }; 