/**
 * 修正版官方执行器
 * 
 * 基于官方 performAction 函数，但修正了 API 调用问题
 */

const { chromium, firefox, webkit } = require('playwright');
const fs = require('fs');
const path = require('path');
const { EventEmitter } = require('events');

// 导入官方模块
const { RecorderCollection } = require('../../node_modules/playwright-core/lib/server/recorder/recorderCollection.js');
const { mainFrameForAction, buildFullSelector } = require('../../node_modules/playwright-core/lib/server/recorder/recorderUtils.js');
const { serverSideCallMetadata } = require('playwright-core/lib/server');

console.log('✅ 成功导入官方模块');

/**
 * 修正版的 performAction 函数
 * 基于官方实现，但使用正确的 API 调用方式
 */
async function fixedPerformAction(pageAliases, actionInContext) {
  const callMetadata = serverSideCallMetadata();
  const mainFrame = mainFrameForAction(pageAliases, actionInContext);
  const { action } = actionInContext;
  const kActionTimeout = 5000;
  
  if (action.name === "navigate") {
    // 修正：不传递 callMetadata 参数
    await mainFrame.goto(action.url, { timeout: kActionTimeout });
    return;
  }
  
  if (action.name === "openPage")
    throw Error("Not reached");
    
  if (action.name === "closePage") {
    // 修正：不传递 callMetadata 参数
    await mainFrame._page.close();
    return;
  }
  
  const selector = buildFullSelector(actionInContext.frame.framePath, action.selector);
  
  if (action.name === "click") {
    const options = toClickOptions(action);
    // 修正：不传递 callMetadata 参数
    await mainFrame.click(selector, { ...options, timeout: kActionTimeout, strict: true });
    return;
  }
  
  if (action.name === "press") {
    const modifiers = toKeyboardModifiers(action.modifiers);
    const shortcut = [...modifiers, action.key].join("+");
    // 修正：不传递 callMetadata 参数
    await mainFrame.press(selector, shortcut, { timeout: kActionTimeout, strict: true });
    return;
  }
  
  if (action.name === "fill") {
    // 修正：不传递 callMetadata 参数
    await mainFrame.fill(selector, action.text, { timeout: kActionTimeout, strict: true });
    return;
  }
  
  if (action.name === "check") {
    // 修正：不传递 callMetadata 参数
    await mainFrame.check(selector, { timeout: kActionTimeout, strict: true });
    return;
  }
  
  if (action.name === "uncheck") {
    // 修正：不传递 callMetadata 参数
    await mainFrame.uncheck(selector, { timeout: kActionTimeout, strict: true });
    return;
  }
  
  if (action.name === "select") {
    const values = action.options.map((value) => ({ value }));
    // 修正：不传递 callMetadata 参数
    await mainFrame.selectOption(selector, values, { timeout: kActionTimeout, strict: true });
    return;
  }

  // 添加断言动作支持
  if (action.name === "assertVisible") {
    // 检查元素是否可见
    await mainFrame.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
    return;
  }

  if (action.name === "assertHidden") {
    // 检查元素是否隐藏
    await mainFrame.waitForSelector(selector, { state: 'hidden', timeout: kActionTimeout, strict: true });
    return;
  }

  if (action.name === "assertText") {
    // 检查元素文本
    const element = mainFrame.locator(selector).first();
    await element.waitFor({ state: 'visible', timeout: kActionTimeout });

    const actualText = await element.textContent();
    if (action.substring) {
      if (!actualText.includes(action.text)) {
        throw new Error(`Text assertion failed: expected "${actualText}" to contain "${action.text}"`);
      }
    } else {
      if (actualText !== action.text) {
        throw new Error(`Text assertion failed: expected "${action.text}", got "${actualText}"`);
      }
    }
    return;
  }

  if (action.name === "assertValue") {
    // 检查输入框值
    const element = mainFrame.locator(selector).first();
    await element.waitFor({ state: 'visible', timeout: kActionTimeout });
    const actualValue = await element.inputValue();
    if (actualValue !== action.value) {
      throw new Error(`Value assertion failed: expected "${action.value}", got "${actualValue}"`);
    }
    return;
  }

  if (action.name === "assertChecked") {
    // 检查复选框状态
    const element = mainFrame.locator(selector).first();
    await element.waitFor({ state: 'visible', timeout: kActionTimeout });
    const isChecked = await element.isChecked();
    if (action.checked !== isChecked) {
      throw new Error(`Checked assertion failed: expected ${action.checked}, got ${isChecked}`);
    }
    return;
  }

  console.warn(`⚠️ 不支持的动作类型: ${action.name}`);
}

// 辅助函数：转换点击选项
function toClickOptions(action) {
  const options = {};
  if (action.button) options.button = action.button;
  if (action.modifiers) options.modifiers = toKeyboardModifiers(action.modifiers);
  if (action.clickCount) options.clickCount = action.clickCount;
  if (action.position) options.position = action.position;
  return options;
}

// 辅助函数：转换键盘修饰符
function toKeyboardModifiers(modifiers) {
  const result = [];
  if (modifiers & 1) result.push('Alt');
  if (modifiers & 2) result.push('ControlOrMeta');
  if (modifiers & 4) result.push('ControlOrMeta');
  if (modifiers & 8) result.push('Shift');
  return result;
}

/**
 * 修正版的官方执行器
 */
class FixedOfficialExecutor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      timeout: 15000,
      enableDiagnostics: true,
      ...options
    };
    
    this.browser = null;
    this.context = null;
    this.pages = new Map();
    this.pageAliases = new Map();
    this.recorderCollection = null;
    this.pageCounter = 0;
  }

  async initialize(config) {
    try {
      console.log('🚀 初始化修正版官方执行器...');
      
      const browserType = {
        'chromium': chromium,
        'firefox': firefox,
        'webkit': webkit
      }[config.browserName] || chromium;
      
      this.browser = await browserType.launch({
        headless: false,
        ...config.launchOptions
      });
      
      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 },
        ...config.contextOptions
      });
      
      this._setupPageListeners();
      
      const page = await this.context.newPage();
      await this._registerPage(page, 'page');
      
      // 创建官方的 RecorderCollection
      this.recorderCollection = new RecorderCollection(this.pageAliases);
      this.recorderCollection.setEnabled(true);
      
      console.log('✅ 修正版官方执行器初始化完成');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  _setupPageListeners() {
    this.context.on('page', async (page) => {
      const pageAlias = this._generatePageAlias();
      await this._registerPage(page, pageAlias);
      console.log(`📄 新页面创建: ${pageAlias}`);
    });
  }

  async _registerPage(page, pageAlias) {
    this.pages.set(pageAlias, page);
    this.pageAliases.set(page, pageAlias);
    
    page.on('close', () => {
      console.log(`📄 页面关闭: ${pageAlias}`);
      this.pages.delete(pageAlias);
      this.pageAliases.delete(page);
    });
  }

  _generatePageAlias() {
    if (this.pageCounter === 0) {
      this.pageCounter++;
      return 'page';
    } else {
      this.pageCounter++;
      return `page${this.pageCounter}`;  // 修复：现在会生成 page1, page2, page3...
    }
  }

  /**
   * 确保指定的页面别名存在，如果不存在则创建
   * @param {string} pageAlias - 页面别名
   * @private
   */
  async _ensurePageExists(pageAlias) {
    // 检查页面是否已存在
    if (this.pages.has(pageAlias)) {
      return;
    }

    // 如果页面不存在，创建新页面
    console.log(`📄 创建缺失的页面: ${pageAlias}`);
    const newPage = await this.context.newPage();
    await this._registerPage(newPage, pageAlias);

    // 更新页面计数器以避免冲突
    if (pageAlias.startsWith('page') && pageAlias !== 'page') {
      const num = parseInt(pageAlias.substring(4));
      if (!isNaN(num) && num > this.pageCounter) {
        this.pageCounter = num;
      }
    }
  }

  async executeAction(actionData) {
    try {
      if (this.options.enableDiagnostics) {
        console.log(`🎬 开始执行: ${actionData.name}${actionData.selector ? ` -> ${actionData.selector}` : ''}`);
      }

      const startTime = Date.now();

      // 特殊处理页面级别的动作
      if (actionData.name === 'openPage') {
        await this._handleOpenPage(actionData);
      } else if (actionData.name === 'closePage') {
        await this._handleClosePage(actionData);
      } else {
        // 检查页面别名是否存在，如果不存在则创建
        const pageAlias = actionData.pageAlias || 'page';
        await this._ensurePageExists(pageAlias);

        // 其他动作使用官方 performAction
        const actionInContext = {
          frame: {
            pageAlias: pageAlias,
            framePath: actionData.framePath || [],
            isMainFrame: actionData.isMainFrame !== false
          },
          action: {
            name: actionData.name,
            selector: actionData.selector,
            signals: actionData.signals || [],
            ...this._extractActionData(actionData)
          },
          startTime: startTime
        };

        await fixedPerformAction(this.pageAliases, actionInContext);
      }
      
      if (this.options.enableDiagnostics) {
        const duration = Date.now() - startTime;
        console.log(`✅ 执行完成: ${actionData.name} (耗时: ${duration}ms)`);
      }
      
    } catch (error) {
      console.error(`❌ 执行动作失败: ${actionData.name} - ${error.message}`);

      // 添加调试信息
      if (this.options.enableDiagnostics) {
        console.error('📊 调试信息:');
        console.error(`   动作数据:`, JSON.stringify(actionData, null, 2));
        console.error(`   当前页面别名:`, Array.from(this.pages.keys()));
        console.error(`   页面别名映射:`, Array.from(this.pageAliases.values()));
      }

      throw error;
    }
  }

  /**
   * 处理打开新页面动作
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _handleOpenPage(actionData) {
    console.log(`🌐 打开新页面: ${actionData.url}`);
    
    try {
      const newPage = await this.context.newPage();
      const pageAlias = actionData.pageAlias || this._generatePageAlias();
      
      await this._registerPage(newPage, pageAlias);
      
      if (actionData.url) {
        await newPage.goto(actionData.url, {
          timeout: 30000,
          waitUntil: 'domcontentloaded'
        });
      }
      
      console.log(`✅ 新页面创建完成: ${pageAlias} -> ${newPage.url()}`);
      
    } catch (error) {
      console.error(`❌ 打开页面失败:`, error);
      throw error;
    }
  }

  /**
   * 处理关闭页面动作
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _handleClosePage(actionData) {
    const pageAlias = actionData.pageAlias || 'page';
    const page = this.pages.get(pageAlias);
    
    if (page && !page.isClosed()) {
      console.log(`🔒 关闭页面: ${pageAlias}`);
      await page.close();
    } else {
      console.warn(`⚠️ 页面不存在或已关闭: ${pageAlias}`);
    }
  }

  _extractActionData(actionData) {
    const result = {};
    
    switch (actionData.name) {
      case 'navigate':
        result.url = actionData.url || '';
        break;
      case 'click':
        result.button = actionData.button || 'left';
        result.modifiers = actionData.modifiers || 0;
        result.clickCount = actionData.clickCount || 1;
        if (actionData.position) result.position = actionData.position;
        break;
      case 'fill':
        result.text = actionData.text || '';
        break;
      case 'press':
        result.key = actionData.key || '';
        result.modifiers = actionData.modifiers || 0;
        break;
      case 'select':
        result.options = actionData.options || [];
        break;
    }
    
    return result;
  }

  async executeJsonFile(jsonFile) {
    const lines = fs.readFileSync(jsonFile, 'utf-8').split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 文件为空');
    }
    
    const config = JSON.parse(lines[0]);
    await this.initialize(config);
    
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  async executeJsonString(jsonString) {
    const lines = jsonString.split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 数据为空');
    }
    
    const config = JSON.parse(lines[0]);
    await this.initialize(config);
    
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  getCurrentPage() {
    return this.pages.get('page');
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.pages.clear();
      this.pageAliases.clear();
    }
  }
}

module.exports = { 
  FixedOfficialExecutor,
  fixedPerformAction
}; 