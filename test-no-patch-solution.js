/**
 * 测试不使用patch的解决方案
 * 验证是否可以通过监听BrowserContext的RecorderEvent来获取代码生成
 */

const { chromium } = require('playwright-core');

// 尝试导入官方的代码生成API
let generateCode, languageSet, collapseActions;
try {
  // 这些是内部API，可能不会被暴露
  generateCode = require('playwright-core/lib/server/codegen/language').generateCode;
  languageSet = require('playwright-core/lib/server/codegen/languages').languageSet;
  collapseActions = require('playwright-core/lib/server/recorder/recorderUtils').collapseActions;
  console.log('✅ 成功导入官方代码生成API');
} catch (error) {
  console.log('❌ 无法导入官方代码生成API:', error.message);
  console.log('这意味着这些API不是公开的，需要patch才能使用');
}

async function testNoPathSolution() {
  console.log('\n🧪 测试不使用patch的解决方案...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  
  // 收集的actions
  const collectedActions = [];
  
  try {
    // 监听BrowserContext的RecorderEvent
    context.on('recorderevent', (event) => {
      console.log('📡 收到RecorderEvent:', event.event, event.data?.action?.name);
      
      if (event.event === 'actionAdded' || event.event === 'actionUpdated') {
        collectedActions.push(event.data);
        
        // 尝试使用官方API生成代码
        if (generateCode && languageSet && collapseActions) {
          try {
            const collapsed = collapseActions(collectedActions);
            const languageGenerators = Array.from(languageSet());
            const jsGenerator = languageGenerators.find(g => g.id === 'javascript');
            
            if (jsGenerator) {
              const options = {
                browserName: 'chromium',
                launchOptions: { headless: false },
                contextOptions: {},
              };
              
              const result = generateCode(collapsed, jsGenerator, options);
              console.log('🎯 生成的JavaScript代码:');
              console.log(result.text);
              console.log('---');
            }
          } catch (error) {
            console.log('❌ 代码生成失败:', error.message);
          }
        }
      }
    });
    
    // 启用录制器 - API模式
    console.log('🎬 启用API模式录制器...');
    await context._enableRecorder({
      language: 'javascript',
      mode: 'recording',
      recorderMode: 'api'  // 关键：API模式
    });
    
    const page = await context.newPage();
    await page.goto('https://example.com');
    
    // 执行一些操作
    console.log('🖱️ 执行测试操作...');
    await page.click('h1');
    await page.waitForTimeout(1000);
    
    console.log(`\n📊 总共收集到 ${collectedActions.length} 个actions`);
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
  }
  
  await browser.close();
}

async function testWithPWCodegenNoInspector() {
  console.log('\n🧪 测试PW_CODEGEN_NO_INSPECTOR环境变量...\n');
  
  // 设置环境变量
  process.env.PW_CODEGEN_NO_INSPECTOR = '1';
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  
  const collectedActions = [];
  
  context.on('recorderevent', (event) => {
    console.log('📡 PW_CODEGEN_NO_INSPECTOR模式收到事件:', event.event);
    collectedActions.push(event);
  });
  
  try {
    // 尝试启用录制器
    await context._enableRecorder({
      language: 'javascript',
      mode: 'recording'
    });
    
    const page = await context.newPage();
    await page.goto('https://example.com');
    await page.click('h1');
    await page.waitForTimeout(1000);
    
    console.log(`📊 PW_CODEGEN_NO_INSPECTOR模式收集到 ${collectedActions.length} 个事件`);
    
  } catch (error) {
    console.log('❌ PW_CODEGEN_NO_INSPECTOR测试失败:', error.message);
  }
  
  await browser.close();
  delete process.env.PW_CODEGEN_NO_INSPECTOR;
}

async function main() {
  console.log('🎯 深入分析Playwright录制机制\n');
  
  // 检查官方API可用性
  console.log('1. 检查官方代码生成API可用性:');
  if (generateCode && languageSet && collapseActions) {
    console.log('   ✅ generateCode: 可用');
    console.log('   ✅ languageSet: 可用');  
    console.log('   ✅ collapseActions: 可用');
    console.log('   🎉 理论上可以不用patch！');
  } else {
    console.log('   ❌ 官方API不可用，必须使用patch');
  }
  
  // 测试不使用patch的方案
  await testNoPathSolution();
  
  // 测试环境变量方案
  await testWithPWCodegenNoInspector();
  
  console.log('\n📋 结论:');
  console.log('1. 官方代码生成API是否公开暴露？');
  console.log('2. API模式下是否能监听到RecorderEvent？');
  console.log('3. PW_CODEGEN_NO_INSPECTOR是否完全阻止事件？');
}

if (require.main === module) {
  main().catch(console.error);
}
