/**
 * 详细分析Playwright录制机制和SignalAdded事件的影响
 */

const { chromium } = require('playwright-core');
const fs = require('fs');

async function analyzeRecorderEvents() {
  console.log('🔍 详细分析Playwright录制事件机制\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  
  const eventLog = [];
  let actionCount = 0;
  let signalCount = 0;
  
  // 监听所有可能的事件
  context.on('recorderevent', (event) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      event: event.event,
      hasData: !!event.data,
      dataType: event.data ? typeof event.data : null,
      actionName: event.data?.action?.name,
      signalName: event.data?.signal?.name,
      pageGuid: event.data?.frame?.pageGuid
    };
    
    eventLog.push(logEntry);
    
    if (event.event === 'actionAdded' || event.event === 'actionUpdated') {
      actionCount++;
      console.log(`📝 ${event.event}: ${event.data?.action?.name} (总计: ${actionCount})`);
    }
    
    if (event.event === 'signalAdded') {
      signalCount++;
      console.log(`🔔 signalAdded: ${event.data?.signal?.name} (总计: ${signalCount})`);
    }
  });
  
  try {
    // 测试正常模式（会创建RecorderApp）
    console.log('🎬 测试正常录制模式...');
    await context._enableRecorder({
      language: 'javascript',
      mode: 'recording',
      // 不设置recorderMode，默认会创建RecorderApp
    });
    
    const page = await context.newPage();
    await page.goto('https://example.com');
    
    // 执行各种操作来触发不同的事件
    console.log('🖱️ 执行点击操作...');
    await page.click('h1');
    await page.waitForTimeout(500);
    
    console.log('⌨️ 执行输入操作...');
    // 假设页面有输入框
    try {
      await page.fill('input', 'test input');
    } catch (e) {
      console.log('   (页面没有输入框，跳过)');
    }
    await page.waitForTimeout(500);
    
    console.log('🔄 执行导航操作...');
    await page.goto('https://httpbin.org/html');
    await page.waitForTimeout(500);
    
    console.log('🖱️ 再次点击...');
    await page.click('h1');
    await page.waitForTimeout(1000);
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
  }
  
  await browser.close();
  
  // 分析结果
  console.log('\n📊 事件分析结果:');
  console.log(`总事件数: ${eventLog.length}`);
  console.log(`ActionAdded/Updated: ${actionCount}`);
  console.log(`SignalAdded: ${signalCount}`);
  
  // 按事件类型分组
  const eventTypes = {};
  eventLog.forEach(entry => {
    eventTypes[entry.event] = (eventTypes[entry.event] || 0) + 1;
  });
  
  console.log('\n📈 事件类型统计:');
  Object.entries(eventTypes).forEach(([type, count]) => {
    console.log(`  ${type}: ${count}`);
  });
  
  // 保存详细日志
  fs.writeFileSync('recorder-events-log.json', JSON.stringify(eventLog, null, 2));
  console.log('\n💾 详细事件日志已保存到 recorder-events-log.json');
  
  return { eventLog, actionCount, signalCount };
}

async function testApiModeVsNormalMode() {
  console.log('\n🔬 对比API模式 vs 正常模式\n');
  
  // 测试API模式
  console.log('1️⃣ 测试API模式:');
  const apiResults = await testMode('api');
  
  // 测试正常模式  
  console.log('\n2️⃣ 测试正常模式:');
  const normalResults = await testMode('default');
  
  console.log('\n📊 对比结果:');
  console.log(`API模式事件数: ${apiResults.eventCount}`);
  console.log(`正常模式事件数: ${normalResults.eventCount}`);
  console.log(`API模式Actions: ${apiResults.actionCount}`);
  console.log(`正常模式Actions: ${normalResults.actionCount}`);
}

async function testMode(recorderMode) {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  
  let eventCount = 0;
  let actionCount = 0;
  
  context.on('recorderevent', (event) => {
    eventCount++;
    if (event.event === 'actionAdded' || event.event === 'actionUpdated') {
      actionCount++;
    }
    console.log(`  📡 ${event.event}: ${event.data?.action?.name || event.data?.signal?.name || 'unknown'}`);
  });
  
  try {
    const options = {
      language: 'javascript',
      mode: 'recording'
    };
    
    if (recorderMode === 'api') {
      options.recorderMode = 'api';
    }
    
    await context._enableRecorder(options);
    
    const page = await context.newPage();
    await page.goto('https://example.com');
    await page.click('h1');
    await page.waitForTimeout(1000);
    
  } catch (error) {
    console.log(`  ❌ ${recorderMode}模式测试失败:`, error.message);
  }
  
  await browser.close();
  return { eventCount, actionCount };
}

async function main() {
  console.log('🎯 深入分析你的两个问题\n');
  
  console.log('问题1: 能否不用patch直接监听actionUpdated？');
  console.log('问题2: 是否只监听actionUpdated就足够了？\n');
  
  // 详细分析事件机制
  const results = await analyzeRecorderEvents();
  
  // 对比不同模式
  await testApiModeVsNormalMode();
  
  console.log('\n🎯 结论分析:');
  console.log('1. 官方代码生成API不公开暴露');
  console.log('2. API模式下RecorderEvent事件数量:', results.actionCount > 0 ? '有事件' : '无事件');
  console.log('3. SignalAdded事件对代码生成的影响需要进一步分析');
  
  if (results.signalCount > 0) {
    console.log('4. ⚠️  发现SignalAdded事件，这可能影响最终代码生成的完整性');
  } else {
    console.log('4. ✅ 未发现SignalAdded事件，可能只监听ActionAdded就足够');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
