diff --git a/node_modules/playwright-core/lib/server/recorder.js b/node_modules/playwright-core/lib/server/recorder.js
index fd5a574..150eb82 100644
--- a/node_modules/playwright-core/lib/server/recorder.js
+++ b/node_modules/playwright-core/lib/server/recorder.js
@@ -306,7 +306,53 @@ class Recorder {
   }
   _pushAllSources() {
     const primaryPage = this._context.pages()[0];
-    this._recorderApp?.setSources([...this._recorderSources, ...this._userSources.values()], primaryPage?.mainFrame().url());
+    const sources = [...this._recorderSources, ...this._userSources.values()];
+    const primaryPageURL = primaryPage?.mainFrame().url();
+
+    // 原有逻辑：发送到工具栏
+    this._recorderApp?.setSources(sources, primaryPageURL);
+
+    // ELECTRON_BRIDGE_PATCH_APPLIED - Electron 集成补丁
+    if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
+      try {
+        // 直接通过 Electron IPC 发送数据到主进程
+        const electronData = {
+          type: 'playwrightCodeGenerated',
+          sources: sources,
+          primaryPageURL: primaryPageURL,
+          timestamp: Date.now(),
+          mode: this._mode,
+          recorderSources: this._recorderSources,
+          userSources: Array.from(this._userSources.values())
+        };
+
+        // 检查是否在 Electron 环境中
+        if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
+          // 直接使用 Electron 的 IPC 通信
+          const { BrowserWindow } = require('electron');
+          const allWindows = BrowserWindow.getAllWindows();
+
+          // 发送到所有渲染进程
+          allWindows.forEach(window => {
+            if (!window.isDestroyed()) {
+              window.webContents.send('playwright-code-generated', electronData);
+            }
+          });
+
+          // 尝试直接调用全局的 Electron 录制器实例（如果存在）
+          if (global.electronPlaywrightRecorder && global.electronPlaywrightRecorder.messageHandler) {
+            global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated(electronData);
+            console.log('📡 已直接调用 Electron 录制器处理 Playwright 代码生成');
+          } else {
+            console.log('📡 已通过 Electron IPC 发送代码生成数据到所有窗口');
+          }
+        } else {
+          console.log('⚠️ 不在 Electron 环境中，跳过 IPC 通信');
+        }
+      } catch (error) {
+        console.log('⚠️ Electron IPC 集成失败:', error.message);
+      }
+    }
   }
   async onBeforeInputAction(sdkObject, metadata) {
   }
diff --git a/node_modules/playwright-core/lib/server/recorder/recorderRunner.js b/node_modules/playwright-core/lib/server/recorder/recorderRunner.js
index d31b15f..e5e49a3 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderRunner.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderRunner.js
@@ -128,6 +128,151 @@ function toClickOptions(action) {
     options.position = action.position;
   return options;
 }
+
+// GLOBAL_REPLAY_API_PATCH - 将回放接口暴露到全局
+if (typeof global !== 'undefined') {
+  // 确保全局 API 对象存在，如果已存在则合并
+  if (!global.playwrightReplayAPI) {
+    global.playwrightReplayAPI = {};
+  }
+
+  // 创建官方兼容的包装器函数
+  const wrappedPerformAction = async function(pageAliases, actionInContext) {
+    try {
+      // 首先尝试使用官方的 performAction
+      return await performAction(pageAliases, actionInContext);
+    } catch (error) {
+      // 更精确地检测 callMetadata 相关错误
+      const isCallMetadataError = error.message && (
+        error.message.includes('expected string, got object') ||
+        error.message.includes('callMetadata') ||
+        error.message.includes('serverSideCallMetadata') ||
+        (error.message.includes('url') && error.message.includes('expected string'))
+      );
+
+      if (isCallMetadataError) {
+        console.log('🔄 检测到 callMetadata 兼容性问题，使用备用方案...');
+
+        // 备用方案：直接调用底层 API，不使用 callMetadata
+        const mainFrame = (0, import_recorderUtils.mainFrameForAction)(pageAliases, actionInContext);
+        const { action } = actionInContext;
+        const kActionTimeout = 5000;
+
+        if (action.name === "navigate") {
+          await mainFrame.goto(action.url, { timeout: kActionTimeout });
+          return;
+        }
+
+        if (action.name === "closePage") {
+          await mainFrame._page.close();
+          return;
+        }
+
+        const selector = (0, import_recorderUtils.buildFullSelector)(actionInContext.frame.framePath, action.selector);
+
+        if (action.name === "click") {
+          const options = toClickOptions(action);
+          await mainFrame.click(selector, { ...options, timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "fill") {
+          await mainFrame.fill(selector, action.text, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "press") {
+          const modifiers = (0, import_language.toKeyboardModifiers)(action.modifiers);
+          const shortcut = [...modifiers, action.key].join("+");
+          await mainFrame.press(selector, shortcut, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "check") {
+          await mainFrame.check(selector, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "uncheck") {
+          await mainFrame.uncheck(selector, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "select") {
+          const values = action.options.map((value) => ({ value }));
+          await mainFrame.selectOption(selector, [], values, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        // 添加断言动作支持
+        if (action.name === "assertText") {
+          const actualText = await mainFrame.textContent(selector, { timeout: kActionTimeout, strict: true });
+          const expectedText = action.text || '';
+          const substring = action.substring !== false;
+
+          if (substring) {
+            if (!actualText || !actualText.includes(expectedText)) {
+              throw new Error(`断言失败: 元素 ${selector} 文本不包含 "${expectedText}"，实际文本: "${actualText}"`);
+            }
+          } else {
+            if (actualText !== expectedText) {
+              throw new Error(`断言失败: 元素 ${selector} 文本不匹配，期望: "${expectedText}"，实际: "${actualText}"`);
+            }
+          }
+          return;
+        }
+
+        if (action.name === "assertValue") {
+          const actualValue = await mainFrame.inputValue(selector, { timeout: kActionTimeout, strict: true });
+          const expectedValue = action.value || '';
+          if (actualValue !== expectedValue) {
+            throw new Error(`断言失败: 元素 ${selector} 值不匹配，期望: "${expectedValue}"，实际: "${actualValue}"`);
+          }
+          return;
+        }
+
+        if (action.name === "assertChecked") {
+          const isChecked = await mainFrame.isChecked(selector, { timeout: kActionTimeout, strict: true });
+          if (isChecked !== (action.checked !== false)) {
+            throw new Error(`断言失败: 元素 ${selector} 期望${action.checked !== false ? '选中' : '未选中'}，实际${isChecked ? '选中' : '未选中'}`);
+          }
+          return;
+        }
+
+        if (action.name === "assertVisible") {
+          const isVisible = await mainFrame.isVisible(selector, { timeout: kActionTimeout, strict: true });
+          if (!isVisible) {
+            throw new Error(`断言失败: 元素 ${selector} 不可见`);
+          }
+          return;
+        }
+
+        throw new Error("Internal error: unexpected action " + action.name);
+      }
+
+      // 重新抛出其他错误
+      throw error;
+    }
+  };
+
+  // 合并核心回放函数到全局对象（不覆盖已有的函数）
+  Object.assign(global.playwrightReplayAPI, {
+    performAction: wrappedPerformAction,  // 使用包装版本
+    performActionOriginal: performAction,  // 保留原版供参考
+    toClickOptions: toClickOptions,
+    // 从 recorderUtils 导入的辅助函数
+    buildFullSelector: require('./recorderUtils').buildFullSelector,
+    mainFrameForAction: require('./recorderUtils').mainFrameForAction,
+    frameForAction: require('./recorderUtils').frameForAction,
+    // 版本信息
+    version: require('../../../package.json').version || 'unknown',
+    // 标记这是通过patch暴露的API
+    isPatchedAPI: true,
+    patchVersion: '1.0.0'
+  });
+  console.log('🌍 Playwright 回放 API 已暴露到 global.playwrightReplayAPI');
+}
+
 // Annotate the CommonJS export names for ESM import in node:
 0 && (module.exports = {
   performAction,
diff --git a/node_modules/playwright-core/lib/server/recorder/recorderUtils.js b/node_modules/playwright-core/lib/server/recorder/recorderUtils.js
index 6f7b2c4..ac8f94a 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderUtils.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderUtils.js
@@ -88,6 +88,55 @@ function collapseActions(actions) {
   }
   return result;
 }
+
+// GLOBAL_UTILS_API_PATCH - 将工具函数暴露到全局
+if (typeof global !== 'undefined') {
+  // 确保全局 API 对象存在
+  if (!global.playwrightReplayAPI) {
+    global.playwrightReplayAPI = {};
+  }
+
+  // 扩展全局 API，添加更多工具函数
+  Object.assign(global.playwrightReplayAPI, {
+    // 工具函数
+    collapseActions: collapseActions,
+    metadataToCallLog: metadataToCallLog,
+
+    // 创建标准的 ActionInContext 对象的辅助函数
+    createActionInContext: function(pageAlias, framePath, action) {
+      return {
+        frame: {
+          pageAlias: pageAlias || 'page',
+          framePath: framePath || []
+        },
+        action: {
+          signals: [],
+          ...action
+        },
+        startTime: Date.now()
+      };
+    },
+
+    // 批量执行动作的辅助函数
+    executeActionSequence: async function(pageAliases, actions) {
+      const results = [];
+      for (const actionInContext of actions) {
+        try {
+          await global.playwrightReplayAPI.performAction(pageAliases, actionInContext);
+          actionInContext.endTime = Date.now();
+          results.push({ success: true, action: actionInContext });
+        } catch (error) {
+          results.push({ success: false, action: actionInContext, error: error.message });
+          throw error; // 重新抛出错误以保持原有行为
+        }
+      }
+      return results;
+    }
+  });
+
+  console.log('🔧 Playwright 工具函数已添加到 global.playwrightReplayAPI');
+}
+
 // Annotate the CommonJS export names for ESM import in node:
 0 && (module.exports = {
   buildFullSelector,
