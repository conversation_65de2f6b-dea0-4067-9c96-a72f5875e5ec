diff --git a/node_modules/playwright-core/lib/server/recorder/recorderApp.js b/node_modules/playwright-core/lib/server/recorder/recorderApp.js
index a1b2c3d..d4e5678 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderApp.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderApp.js
@@ -400,6 +400,118 @@ class ProgrammaticRecorderApp {
   static async run(inspectedContext, recorder) {
     let lastAction = null;
     
-    // ELECTRON_API_MODE_PATCH - 为API模式添加代码生成能力
+    // ELECTRON_API_MODE_PATCH_V2 - 为API模式添加完整的代码生成能力
     const apiModeCodeGenerator = {
       actions: [],
       languageGeneratorOptions: {
@@ -431,6 +543,7 @@ class ProgrammaticRecorderApp {
           
           const collapsedActions = collapseActions(this.actions);
           const timestamp = monotonicTime();
           const sources = [];
           
           // 生成所有语言的代码
@@ -476,6 +589,7 @@ class ProgrammaticRecorderApp {
         if (typeof global !== 'undefined' && global.electronPlaywrightRecorder) {
           global.electronPlaywrightRecorder.messageHandler?._handlePlaywrightCodeGenerated(electronData);
           console.log('📡 API模式: 已发送代码生成数据到Electron (动作数量: ' + this.actions.length + ')');
+        } else {
+          console.log('⚠️ 全局Electron录制器不存在，无法发送代码生成数据');
         }
       }
     };
@@ -498,8 +612,24 @@ class ProgrammaticRecorderApp {
         }, 100);
       }
     });
     
+    // 🔥 关键修复：添加SignalAdded事件处理
     recorder.on('signalAdded', signal => {
       const page = findPageByGuid(inspectedContext, signal.frame.pageGuid);
       inspectedContext.emit('recorderEvent', { event: 'signalAdded', data: signal, page });
+      
+      // API模式信号处理增强
+      if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE || process.env.PLAYWRIGHT_API_CODE_GENERATION) {
+        // 将信号添加到最后一个匹配的动作中
+        const lastAction = apiModeCodeGenerator.actions.findLast(
+          a => a.frame.pageGuid === signal.frame.pageGuid
+        );
        
+        if (lastAction) {
+          if (!lastAction.action.signals) lastAction.action.signals = [];
+          lastAction.action.signals.push(signal.signal);
+          console.log('🔗 信号 ' + signal.signal.name + ' 已添加到动作 ' + lastAction.action.name);
+          
+          // 立即重新生成代码
+          clearTimeout(apiModeCodeGenerator._timeout);
+          apiModeCodeGenerator._timeout = setTimeout(() => {
+            apiModeCodeGenerator.pushSourcesToGlobal();
+          }, 50); // 更短的延迟，因为信号通常紧跟动作
+        }
+      }
     });
   }
 }
diff --git a/node_modules/playwright-core/lib/server/recorder.js b/node_modules/playwright-core/lib/server/recorder.js
index fd5a574..150eb82 100644
--- a/node_modules/playwright-core/lib/server/recorder.js
+++ b/node_modules/playwright-core/lib/server/recorder.js
@@ -306,7 +306,53 @@ class Recorder {
   }
   _pushAllSources() {
     const primaryPage = this._context.pages()[0];
-    this._recorderApp?.setSources([...this._recorderSources, ...this._userSources.values()], primaryPage?.mainFrame().url());
+    const sources = [...this._recorderSources, ...this._userSources.values()];
+    const primaryPageURL = primaryPage?.mainFrame().url();
+
+    // 原有逻辑：发送到工具栏
+    this._recorderApp?.setSources(sources, primaryPageURL);

+    // ELECTRON_BRIDGE_PATCH_APPLIED - Electron 集成补丁
+    if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
+      try {
+        // 直接通过 Electron IPC 发送数据到主进程
+        const electronData = {
+          type: 'playwrightCodeGenerated',
+          sources: sources,
+          primaryPageURL: primaryPageURL,
+          timestamp: Date.now(),
+          mode: this._mode,
+          recorderSources: this._recorderSources,
+          userSources: Array.from(this._userSources.values())
+        };

+        // 检查是否在 Electron 环境中
+        if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
+          // 直接使用 Electron 的 IPC 通信
+          const { BrowserWindow } = require('electron');
+          const allWindows = BrowserWindow.getAllWindows();

+          // 发送到所有渲染进程
+          allWindows.forEach(window => {
+            if (!window.isDestroyed()) {
+              window.webContents.send('playwright-code-generated', electronData);
+            }
+          });

+          // 尝试直接调用全局的 Electron 录制器实例（如果存在）
+          if (global.electronPlaywrightRecorder && global.electronPlaywrightRecorder.messageHandler) {
+            global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated(electronData);
+            console.log('📡 已直接调用 Electron 录制器处理 Playwright 代码生成');
+          } else {
+            console.log('📡 已通过 Electron IPC 发送代码生成数据到所有窗口');
+          }
+        } else {
+          console.log('⚠️ 不在 Electron 环境中，跳过 IPC 通信');
+        }
+      } catch (error) {
+        console.log('⚠️ Electron IPC 集成失败:', error.message);
+      }
+    }
   }
   async onBeforeInputAction(sdkObject, metadata) {
   } 