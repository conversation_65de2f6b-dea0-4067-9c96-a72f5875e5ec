diff --git a/node_modules/playwright-core/lib/server/recorder/recorderApp.js b/node_modules/playwright-core/lib/server/recorder/recorderApp.js
index a1b2c3d..d4e5678 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderApp.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderApp.js
@@ -400,6 +400,89 @@ class ProgrammaticRecorderApp {
   static async run(inspectedContext, recorder) {
     let lastAction = null;
+    
+    // ELECTRON_API_MODE_PATCH - 为API模式添加代码生成能力
+    const apiModeCodeGenerator = {
+      actions: [],
+      languageGeneratorOptions: {
+        browserName: inspectedContext._browser.options.name,
+        launchOptions: { headless: false },
+        contextOptions: {},
+        deviceName: undefined,
+        saveStorage: undefined,
+      },
+      
+      // 生成所有语言的脚本代码
+      generateAllSources() {
+        if (this.actions.length === 0) return [];
+        
+        try {
+          const { collapseActions } = require('./recorderUtils');
+          const { generateCode } = require('../codegen/language');
+          const { languageSet } = require('../codegen/languages');
+          const { monotonicTime } = require('../../utils/isomorphic/time');
+          
+          const collapsedActions = collapseActions(this.actions);
+          const timestamp = monotonicTime();
+          const sources = [];
+          
+          // 生成所有语言的代码
+          for (const languageGenerator of languageSet()) {
+            const { header, footer, actionTexts, text } = generateCode(
+              collapsedActions, 
+              languageGenerator, 
+              this.languageGeneratorOptions
+            );
+            
+            const source = {
+              isPrimary: languageGenerator.id === 'javascript',
+              timestamp,
+              isRecorded: true,
+              label: languageGenerator.name,
+              group: languageGenerator.groupName,
+              id: languageGenerator.id,
+              text,
+              header,
+              footer,
+              actions: actionTexts,
+              language: languageGenerator.highlighter,
+              highlight: []
+            };
+            
+            source.revealLine = text.split('\n').length - 1;
+            sources.push(source);
+          }
+          
+          return sources;
+        } catch (error) {
+          console.error('🚨 API模式代码生成失败:', error);
+          return [];
+        }
+      },
+      
+      // 发送生成的代码到全局
+      pushSourcesToGlobal() {
+        const sources = this.generateAllSources();
+        if (sources.length === 0) return;
+        
+        const primaryPage = inspectedContext.pages()[0];
+        const electronData = {
+          type: 'playwrightCodeGenerated',
+          sources: sources,
+          primaryPageURL: primaryPage?.mainFrame().url(),
+          timestamp: Date.now(),
+          mode: recorder.mode(),
+          isApiMode: true,
+          actionCount: this.actions.length
+        };
+        
+        // 发送到全局对象供Electron使用
+        if (typeof global !== 'undefined' && global.electronPlaywrightRecorder) {
+          global.electronPlaywrightRecorder.messageHandler?._handlePlaywrightCodeGenerated(electronData);
+          console.log('📡 API模式: 已发送代码生成数据到Electron (动作数量: ' + this.actions.length + ')');
+        }
+      }
+    };
+    
     recorder.on('actionAdded', action => {
       const page = findPageByGuid(inspectedContext, action.frame.pageGuid);
       if (!page) return;
@@ -409,10 +492,20 @@ class ProgrammaticRecorderApp {
       else
         inspectedContext.emit('recorderEvent', { event: 'actionUpdated', data: action, page });
       lastAction = action;
+      
+      // API模式代码生成增强
+      if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE || process.env.PLAYWRIGHT_API_CODE_GENERATION) {
+        apiModeCodeGenerator.actions.push(action);
+        // 延迟生成，避免频繁触发
+        clearTimeout(apiModeCodeGenerator._timeout);
+        apiModeCodeGenerator._timeout = setTimeout(() => {
+          apiModeCodeGenerator.pushSourcesToGlobal();
+        }, 100);
+      }
     });
+    
     recorder.on('signalAdded', signal => {
       const page = findPageByGuid(inspectedContext, signal.frame.pageGuid);
       inspectedContext.emit('recorderEvent', { event: 'signalAdded', data: signal, page });
     });
   }
-}
\ No newline at end of file
+}
diff --git a/node_modules/playwright-core/lib/server/recorder.js b/node_modules/playwright-core/lib/server/recorder.js
index fd5a574..150eb82 100644
--- a/node_modules/playwright-core/lib/server/recorder.js
+++ b/node_modules/playwright-core/lib/server/recorder.js
@@ -306,7 +306,53 @@ class Recorder {
   }
   _pushAllSources() {
     const primaryPage = this._context.pages()[0];
-    this._recorderApp?.setSources([...this._recorderSources, ...this._userSources.values()], primaryPage?.mainFrame().url());
+    const sources = [...this._recorderSources, ...this._userSources.values()];
+    const primaryPageURL = primaryPage?.mainFrame().url();
+
+    // 原有逻辑：发送到工具栏
+    this._recorderApp?.setSources(sources, primaryPageURL);
+
+    // ELECTRON_BRIDGE_PATCH_APPLIED - Electron 集成补丁
+    if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
+      try {
+        // 直接通过 Electron IPC 发送数据到主进程
+        const electronData = {
+          type: 'playwrightCodeGenerated',
+          sources: sources,
+          primaryPageURL: primaryPageURL,
+          timestamp: Date.now(),
+          mode: this._mode,
+          recorderSources: this._recorderSources,
+          userSources: Array.from(this._userSources.values())
+        };

+        // 检查是否在 Electron 环境中
+        if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
+          // 直接使用 Electron 的 IPC 通信
+          const { BrowserWindow } = require('electron');
+          const allWindows = BrowserWindow.getAllWindows();

+          // 发送到所有渲染进程
+          allWindows.forEach(window => {
+            if (!window.isDestroyed()) {
+              window.webContents.send('playwright-code-generated', electronData);
+            }
+          });

+          // 尝试直接调用全局的 Electron 录制器实例（如果存在）
+          if (global.electronPlaywrightRecorder && global.electronPlaywrightRecorder.messageHandler) {
+            global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated(electronData);
+            console.log('📡 已直接调用 Electron 录制器处理 Playwright 代码生成');
+          } else {
+            console.log('📡 已通过 Electron IPC 发送代码生成数据到所有窗口');
+          }
+        } else {
+          console.log('⚠️ 不在 Electron 环境中，跳过 IPC 通信');
+        }
+      } catch (error) {
+        console.log('⚠️ Electron IPC 集成失败:', error.message);
+      }
+    }
   }
   async onBeforeInputAction(sdkObject, metadata) {
   } 