{"name": "playwright-internal", "private": true, "version": "1.53.1", "description": "A high-level API to automate web browsers", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "scripts": {"ctest": "playwright test --config=tests/library/playwright.config.ts --project=chromium-*", "ftest": "playwright test --config=tests/library/playwright.config.ts --project=firefox-*", "wtest": "playwright test --config=tests/library/playwright.config.ts --project=webkit-*", "atest": "playwright test --config=tests/android/playwright.config.ts", "etest": "playwright test --config=tests/electron/playwright.config.ts", "webview2test": "playwright test --config=tests/webview2/playwright.config.ts", "itest": "playwright test --config=tests/installation/playwright.config.ts", "stest": "playwright test --config=tests/stress/playwright.config.ts", "biditest": "playwright test --config=tests/bidi/playwright.config.ts", "test-html-reporter": "playwright test --config=packages/html-reporter", "test-web": "playwright test --config=packages/web", "ttest": "node ./tests/playwright-test/stable-test-runner/node_modules/@playwright/test/cli test --config=tests/playwright-test/playwright.config.ts", "ct": "playwright test tests/components/test-all.spec.js --reporter=list", "test": "playwright test --config=tests/library/playwright.config.ts", "eslint": "eslint --cache", "tsc": "tsc -p . && tsc -p packages/html-reporter/", "doc": "node utils/doclint/cli.js", "lint": "npm run eslint && npm run tsc && npm run doc && npm run check-deps && node utils/generate_channels.js && node utils/generate_types/ && npm run lint-tests && npm run test-types && npm run lint-packages", "lint-packages": "node utils/workspace.js --ensure-consistent", "lint-tests": "node utils/lint_tests.js", "flint": "concurrently \"npm run eslint\" \"npm run tsc\" \"npm run doc\" \"npm run check-deps\" \"node utils/generate_channels.js\" \"node utils/generate_types/\" \"npm run lint-tests\" \"npm run test-types\" \"npm run lint-packages\" \"node utils/doclint/linting-code-snippets/cli.js --js-only\"", "clean": "node utils/build/clean.js", "build": "node utils/build/build.js", "watch": "node utils/build/build.js --watch --lint", "test-types": "node utils/generate_types/ && tsc -p utils/generate_types/test/tsconfig.json && tsc -p ./tests/", "roll": "node utils/roll_browser.js", "check-deps": "node utils/check_deps.js", "build-android-driver": "./utils/build_android_driver.sh", "innerloop": "playwright run-server --reuse-browser"}, "workspaces": ["packages/*"], "devDependencies": {"@actions/core": "^1.10.0", "@actions/github": "^6.0.0", "@babel/code-frame": "^7.26.2", "@eslint/compat": "^1.2.6", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@octokit/graphql-schema": "^15.26.0", "@stylistic/eslint-plugin": "^3.0.1", "@types/codemirror": "^5.60.7", "@types/formidable": "^2.0.4", "@types/immutable": "^3.8.7", "@types/node": "^18.19.68", "@types/react": "^18.0.12", "@types/react-dom": "^18.0.5", "@types/ws": "^8.5.3", "@types/xml2js": "^0.4.9", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/utils": "^8.26.1", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.2.1", "@zip.js/zip.js": "^2.7.29", "ansi-styles": "^4.3.0", "chokidar": "^3.5.3", "chromium-bidi": "^5.3.1", "colors": "^1.4.0", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "electron": "^30.1.2", "esbuild": "^0.25.0", "eslint": "^9.19.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "formidable": "^2.1.1", "immutable": "^4.3.7", "license-checker": "^25.0.1", "mime": "^3.0.0", "node-stream-zip": "^1.15.0", "react": "^18.1.0", "react-dom": "^18.1.0", "ssim.js": "^3.5.0", "typescript": "^5.8.2", "vite": "^6.3.4", "ws": "^8.17.1", "xml2js": "^0.5.0", "yaml": "2.6.0"}}